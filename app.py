import os
import re
import time
import json
import base64
import tempfile
import io
import math
from concurrent.futures import ThreadPoolExecutor, as_completed

# Laad environment variables uit .env bestand
from dotenv import load_dotenv
load_dotenv()

from flask import Flask, request, render_template, jsonify
from flask_socketio import SocketIO, emit
from werkzeug.utils import secure_filename

# --- Optionele / lazy imports voor lokale modellen ---
try:
    import whisper
except Exception:
    whisper = None

try:
    import torch
except Exception:
    torch = None

try:
    import librosa
except Exception:
    librosa = None

try:
    from transformers import Wav2Vec2ForCTC, Wav2Vec2Processor
except Exception:
    Wav2Vec2ForCTC = None
    Wav2Vec2Processor = None

try:
    from pydub import AudioSegment
except Exception:
    AudioSegment = None

import requests

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-for-websockets')

# Enable logging for debugging RunPod API issues
import logging
logging.basicConfig(level=logging.INFO)
app.logger.setLevel(logging.INFO)

# Initialize SocketIO for real-time communication
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# =========================
#   MODEL & API CONFIG
# =========================
MODEL_TYPE = os.getenv('MODEL_TYPE', 'runpod')  # 'runpod' | 'whisper' | 'wav2vec2'

# Whisper (lokaal)
WHISPER_MODEL_SIZE = os.getenv('WHISPER_MODEL_SIZE', 'medium')

# Wav2Vec2 (lokaal)
WAV2VEC2_MODEL_ID = os.getenv('WAV2VEC2_MODEL_ID', 'facebook/wav2vec2-large-xlsr-53-dutch')
# Alternatief:
# WAV2VEC2_MODEL_ID = "jonatasgrosman/wav2vec2-large-xlsr-53-dutch"

# RunPod Serverless Faster-Whisper
RUNPOD_API_KEY = os.getenv('RUNPOD_API_KEY','rpa_8OE1WN9C8SFIUSDKPK4TU56NDDULZT6IU8LETSTV11nz9o')
RUNPOD_ENDPOINT_ID = os.getenv('RUNPOD_ENDPOINT_ID','2n7nj4xtwcgsl9')
RUNPOD_BASE_URL = os.getenv('RUNPOD_BASE_URL', 'https://api.runpod.ai/v2')
RUNPOD_MODEL = os.getenv('RUNPOD_MODEL', 'large-v3')  # bv. turbo | base | small | medium | large-v3 | distil-large-v3
RUNPOD_TRANSCRIPTION = os.getenv('RUNPOD_TRANSCRIPTION', 'plain_text')  # plain_text | formatted_text | srt | vtt
RUNPOD_TRANSLATE = 'false' #os.getenv('RUNPOD_TRANSLATE', 'false').lower() == 'true'
RUNPOD_WORD_TS = 'false' #os.getenv('RUNPOD_WORD_TIMESTAMPS', 'false').lower() == 'true'
RUNPOD_TIMEOUT = int(os.getenv('RUNPOD_TIMEOUT', '60'))  # agressiever voor snellere fail

# Chunking configuratie voor snellere transcriptie
CHUNK_MS = int(os.getenv('RUNPOD_CHUNK_MS', '5000'))        # ~2s chunks
OVERLAP_MS = int(os.getenv('RUNPOD_OVERLAP_MS', '1000'))     # 0.5s overlap
MAX_WORKERS = int(os.getenv('RUNPOD_MAX_WORKERS', '4'))     # parallel verzoeken

# Groq (rapportgeneratie)
GROQ_API_KEY = os.getenv('GROQ_API_KEY','********************************************************')
GROQ_MODEL = os.getenv('GROQ_MODEL', 'deepseek-r1-distill-llama-70b')
GROQ_URL = "https://api.groq.com/openai/v1/chat/completions"

ALLOWED_EXTENSIONS = {'wav', 'mp3', 'm4a', 'flac', 'aac', 'webm', 'ogg'}

# Preferred audio formats for RunPod (in order of preference)
PREFERRED_FORMATS = ['wav', 'mp3', 'flac', 'ogg', 'm4a', 'aac', 'webm']
ERROR_NO_TRANSCRIPTION = "[ERROR: Alleen status ontvangen, geen transcriptie]"

# =========================
#   GLOBALS VOOR MODELS & HTTP SESSION
# =========================
whisper_model = None
wav2vec2_model = None
wav2vec2_processor = None

# HTTP Session voor RunPod (keep-alive)
_runpod_session = None

# Streaming transcription buffer per session
_streaming_sessions = {}

class StreamingSession:
    """Class voor het beheren van streaming transcriptie sessies."""
    def __init__(self, session_id: str, language: str = "nl"):
        self.session_id = session_id
        self.language = language
        self.audio_buffer = io.BytesIO()
        self.transcribed_chunks = []
        self.pending_audio = b''
        self.chunk_counter = 0
        self.is_active = True
        self.last_activity = time.time()
    
    def add_audio_data(self, audio_data: bytes):
        """Voeg audio data toe aan de buffer (webm format)."""
        if not self.is_active:
            return
        
        # Voor webm/opus data bufferen we anders
        self.audio_buffer.write(audio_data)
        self.last_activity = time.time()
        
        # We verwerken elke chunk die binnenkomt (1 seconde intervals van frontend)
        self.chunk_counter += 1
        
        # Return de webm data direct voor transcriptie
        return {
            'chunk_id': self.chunk_counter,
            'audio_data': audio_data,  # webm/opus data
            'session_id': self.session_id
        }
    
    def _process_chunk(self):
        """Verwerk een audio chunk voor transcriptie."""
        # Not used anymore, maar houden voor backwards compatibility
        return None
    
    def finalize(self):
        """Finaliseer de sessie en verwerk resterende audio."""
        self.is_active = False
        # Voor streaming verwerken we geen resterende audio meer
        # omdat elke chunk al verwerkt is
        return None

def get_runpod_session():
    """Get of maak een herbruikbare HTTP session voor RunPod API."""
    global _runpod_session
    if _runpod_session is None:
        _runpod_session = requests.Session()
        _runpod_session.headers.update({
            "Authorization": f"Bearer {RUNPOD_API_KEY}",
            "Accept": "application/json",
            "Content-Type": "application/json",
        })
    return _runpod_session


# =========================
#   HULPFUNCTIES
# =========================
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


# =========================
#   AUDIO HELPER FUNCTIONS
# =========================
def load_as_wav16k_mono(audio_path: str):
    """Laad elk audioformaat en forceer 16k mono PCM (kleiner & sneller)."""
    if AudioSegment is None:
        raise RuntimeError("pydub niet beschikbaar. Installeer met: pip install pydub")
    
    try:
        audio = AudioSegment.from_file(audio_path)
        audio = audio.set_channels(1).set_frame_rate(16000).set_sample_width(2)  # 16-bit PCM
        return audio
    except Exception as e:
        app.logger.error(f"Failed to load audio file {audio_path}: {str(e)}")
        # Try with explicit format detection
        file_ext = os.path.splitext(audio_path)[1].lower()
        if file_ext == '.webm':
            try:
                audio = AudioSegment.from_file(audio_path, format="webm")
                audio = audio.set_channels(1).set_frame_rate(16000).set_sample_width(2)
                return audio
            except Exception:
                pass
        raise e


def audiosegment_to_b64_wav(audio) -> str:
    """AudioSegment -> WAV bytes -> base64 (zonder op schijf te schrijven)."""
    buf = io.BytesIO()
    audio.export(buf, format="wav")  # PCM s16le 16k mono
    return base64.b64encode(buf.getvalue()).decode("utf-8")


def load_prompts():
    """
    Laadt de prompts uit prompt.txt
    Retourneert (system_prompt, user_prompt_template)
    """
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        prompt_file = os.path.join(script_dir, 'prompt.txt')

        with open(prompt_file, 'r', encoding='utf-8') as f:
            content = f.read()

        parts = content.split('# User Prompt Template')
        if len(parts) != 2:
            raise ValueError("Prompt bestand heeft niet de verwachte structuur")

        system_part = parts[0].replace('# System Prompt', '').strip()
        user_part = parts[1].strip()
        return system_part, user_part
    except Exception as e:
        app.logger.error(f"Fout bij laden van prompts: {str(e)}")
        # Fallback
        system_prompt = ("Je bent een tekstverbetertool. Je taak is ALLEEN om de ingevoerde tekst te verbeteren "
                         "door vulwoorden zoals 'ehm', 'uhm', 'aah', herhalingen en taalfouten weg te halen. "
                         "Maak de tekst beter leesbaar en vloeiender, maar verander of voeg NIETS toe aan de inhoud. "
                         "Gebruik alleen wat de persoon daadwerkelijk heeft gezegd. Verzin geen nieuwe informatie, "
                         "structuur of kopjes.")
        user_template = "Verbeter alleen deze gesproken tekst door vulwoorden en herhalingen weg te halen, zonder iets toe te voegen: {transcription}"
        return system_prompt, user_template


def strip_think_blocks(text: str) -> str:
    """Verwijdert <think>...</think> en varianten."""
    if not text:
        return ""
    cleaned = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL | re.IGNORECASE)
    cleaned = re.sub(r"<thinking>.*?</thinking>", "", cleaned, flags=re.DOTALL | re.IGNORECASE)
    cleaned = re.sub(r'\n\s*\n', '\n', cleaned).strip()
    return cleaned


# =========================
#   LOKALE WHISPER
# =========================
def get_whisper_model():
    global whisper_model
    if whisper is None:
        raise RuntimeError("Whisper lib niet beschikbaar. Installeer 'openai-whisper' of kies MODEL_TYPE=runpod.")
    if whisper_model is None:
        print(f"Loading Whisper model '{WHISPER_MODEL_SIZE}' (first time)...")
        os.environ['WHISPER_CACHE_DIR'] = 'models'
        whisper_model = whisper.load_model(WHISPER_MODEL_SIZE, device="cpu", download_root="models")
        print("Whisper model loaded successfully!")
    return whisper_model


def transcribe_with_whisper(audio_path, language='nl'):
    model = get_whisper_model()
    result = model.transcribe(audio_path, language=language, fp16=False)
    return result.get("text", "").strip()


# =========================
#   LOKALE WAV2VEC2
# =========================
def get_wav2vec2_model():
    global wav2vec2_model, wav2vec2_processor
    if Wav2Vec2ForCTC is None or Wav2Vec2Processor is None:
        raise RuntimeError("Transformers Wav2Vec2 niet beschikbaar. Installeer 'transformers' of kies MODEL_TYPE=runpod.")
    if wav2vec2_model is None or wav2vec2_processor is None:
        print(f"Loading Wav2Vec2 model '{WAV2VEC2_MODEL_ID}' (first time)...")
        cache_dir = "models/transformers_cache"
        os.makedirs(cache_dir, exist_ok=True)
        wav2vec2_processor = Wav2Vec2Processor.from_pretrained(WAV2VEC2_MODEL_ID, cache_dir=cache_dir)
        wav2vec2_model = Wav2Vec2ForCTC.from_pretrained(WAV2VEC2_MODEL_ID, cache_dir=cache_dir)
        print("Wav2Vec2 model loaded successfully!")
    return wav2vec2_model, wav2vec2_processor


def transcribe_with_wav2vec2(audio_path):
    if librosa is None or torch is None:
        raise RuntimeError("librosa/torch niet beschikbaar. Installeer dependencies of kies MODEL_TYPE=runpod.")
    model, processor = get_wav2vec2_model()
    speech_array, _ = librosa.load(audio_path, sr=16_000)
    inputs = processor(speech_array, sampling_rate=16_000, return_tensors="pt", padding=True)
    with torch.no_grad():
        logits = model(inputs.input_values, attention_mask=inputs.attention_mask).logits
    predicted_ids = torch.argmax(logits, dim=-1)
    transcription = processor.batch_decode(predicted_ids)[0]
    return transcription.strip()


# =========================
#   RUNPOD SERVERLESS - GEOPTIMALISEERD
# =========================
def _runsync(payload: dict) -> dict:
    """Basis RunPod API call met herbruikbare session."""
    if not RUNPOD_API_KEY or not RUNPOD_ENDPOINT_ID:
        raise RuntimeError("RUNPOD_API_KEY of RUNPOD_ENDPOINT_ID ontbreekt")
    
    url = f"{RUNPOD_BASE_URL}/{RUNPOD_ENDPOINT_ID}/runsync"
    session = get_runpod_session()
    
    r = session.post(url, json=payload, timeout=RUNPOD_TIMEOUT)
    if r.status_code != 200:
        raise RuntimeError(f"RunPod error {r.status_code}: {r.text}")
    
    # Debug: log de volledige response
    response_json = r.json()
    app.logger.info(f"RunPod response: {response_json}")
    
    # Probeer verschillende response formaten
    if "output" in response_json:
        return response_json["output"]
    elif "result" in response_json:
        return response_json["result"]
    elif "data" in response_json:
        return response_json["data"]
    else:
        # Als er geen bekende structuur is, return de hele response voor debugging
        app.logger.warning(f"Unexpected RunPod response structure: {list(response_json.keys())}")
        return response_json


def transcribe_chunk_from_bytes(audio_bytes: bytes, language: str = "nl") -> dict:
    """Transcribeer audio bytes direct (voor streaming - webm/opus format)."""
    if AudioSegment is None:
        raise RuntimeError("pydub niet beschikbaar voor streaming transcriptie")
    
    try:
        # Probeer verschillende audio formaten
        audio_seg = None
        
        # Strategie 1: Probeer in-memory conversie
        formats_to_try = ["webm", "opus", "ogg", None]  # None = auto-detect
        
        for fmt in formats_to_try:
            try:
                if fmt is None:
                    audio_seg = AudioSegment.from_file(io.BytesIO(audio_bytes))
                else:
                    audio_seg = AudioSegment.from_file(io.BytesIO(audio_bytes), format=fmt)
                app.logger.info(f"Successfully decoded audio using format: {fmt or 'auto'}")
                break
            except Exception as e:
                app.logger.debug(f"Failed to decode as {fmt or 'auto'}: {str(e)}")
                continue
        
        # Strategie 2: Probeer via tijdelijk bestand
        if audio_seg is None:
            try:
                with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
                    temp_file.write(audio_bytes)
                    temp_path = temp_file.name
                
                try:
                    audio_seg = AudioSegment.from_file(temp_path)
                    app.logger.info("Successfully decoded audio using temporary file")
                finally:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                        
            except Exception as e:
                app.logger.error(f"Temporary file strategy also failed: {str(e)}")
        
        if audio_seg is not None:
            # Converteer naar 16kHz mono voor optimale prestaties
            audio_seg = audio_seg.set_channels(1).set_frame_rate(16000).set_sample_width(2)
            return transcribe_chunk(audio_seg, language)
        else:
            raise RuntimeError("Could not decode audio with any strategy")
        
    except Exception as e:
        app.logger.error(f"Audio conversion error: {str(e)}")
        
        # Fallback: probeer verschillende base64 formaten
        return _try_direct_audio_transcription(audio_bytes)


def _try_direct_audio_transcription(audio_bytes: bytes) -> dict:
    """Probeer directe transcriptie zonder audio conversie."""
    try:
        # Verstuur de originele audio bytes direct naar RunPod
        audio_b64 = base64.b64encode(audio_bytes).decode("utf-8")
        
        payload = {
            "input": {
                "audio_base64": audio_b64,
                "model": RUNPOD_MODEL,
                "language": "nl",  # Force Nederlands voor directe transcriptie
            }
        }
        
        out = _runsync(payload)
        app.logger.info(f"Direct audio transcription output: {out}")
        
        if isinstance(out, dict):
            # Check voor FAILED status eerst
            if out.get("status") == "FAILED":
                error_msg = out.get("error", "Unknown error")
                app.logger.error(f"RunPod direct transcription failed: {error_msg}")
                if "InvalidDataError" in error_msg:
                    return {"text": "[RunPod Error: Audio format niet ondersteund]", "segments": []}
                else:
                    return {"text": f"[RunPod Error: {error_msg[:50]}...]", "segments": []}
            
            # Voor RunPod responses met output veld
            if "output" in out and isinstance(out["output"], dict):
                output = out["output"]
                transcription = output.get("transcription", "")
                segments = output.get("segments", [])
                return {"text": str(transcription).strip(), "segments": segments}
        
        return {"text": "[ERROR: Geen transcriptie beschikbaar]", "segments": []}
        
    except Exception as e:
        app.logger.error(f"Direct audio transcription failed: {str(e)}")
        return {"text": f"[ERROR: Audio processing failed - {str(e)}]", "segments": []}


def transcribe_chunk(audio_seg, language: str = "nl") -> dict:
    """Transcribeer een enkel audio chunk."""
    app.logger.info(f"Transcribing chunk: duration={len(audio_seg)}ms, channels={audio_seg.channels}, frame_rate={audio_seg.frame_rate}")

    audio_b64 = audiosegment_to_b64_wav(audio_seg)
    app.logger.info(f"Converted to base64 WAV: {len(audio_b64)} characters")

    # Gebruik minimale payload met taal parameter
    payload = {
        "input": {
            "audio_base64": audio_b64,
            "model": RUNPOD_MODEL,
            "language": language if language != 'auto' else 'nl',  # Force Nederlands
        }
    }

    app.logger.info(f"Sending to RunPod: model={RUNPOD_MODEL}, language={language}")
    out = _runsync(payload)

    # Debug logging with detailed response analysis
    app.logger.info(f"RunPod response type: {type(out)}")
    app.logger.info(f"RunPod response keys: {list(out.keys()) if isinstance(out, dict) else 'Not a dict'}")
    app.logger.info(f"Full RunPod response: {out}")

    # Parse RunPod response (nu met werkende output veld)
    if isinstance(out, dict):
        # Check voor FAILED status eerst
        if out.get("status") == "FAILED":
            error_msg = out.get("error", "Unknown error")
            app.logger.error(f"RunPod job failed: {error_msg}")
            if "InvalidDataError" in error_msg:
                return {"text": "[RunPod Error: Audio format niet ondersteund - probeer een ander bestand]", "segments": []}
            else:
                return {"text": f"[RunPod Error: Job failed - {error_msg[:100]}...]", "segments": []}
        
        # Direct transcriptie veld (hoogste prioriteit)
        if out.get("transcription"):
            return {"text": str(out["transcription"]).strip(), "segments": out.get("segments") or []}
        
        # Voor RunPod responses met output veld
        if "output" in out and isinstance(out["output"], dict):
            output = out["output"]
            transcription = output.get("transcription", "")
            segments = output.get("segments", [])
            return {"text": str(transcription).strip(), "segments": segments}
        
        # Fallback naar andere formaten
        if out.get("text"):
            return {"text": str(out["text"]).strip(), "segments": out.get("segments") or []}
        if out.get("joined_text"):
            return {"text": str(out["joined_text"]).strip(), "segments": out.get("segments") or []}
        if out.get("segments"):
            txt = " ".join(seg.get("text", "") for seg in out["segments"])
            return {"text": txt.strip(), "segments": out["segments"]}
        
        # Als het een status response is zonder transcriptie data
        if out.get("status") and not any(key in out for key in ["transcription", "text", "output", "segments"]):
            app.logger.error(f"RunPod endpoint retourneert alleen metadata: {out}")
            return {"text": f"[RunPod Error: Alleen metadata ontvangen - {out.get('status')}]", "segments": []}
    
    # fallback
    return {"text": str(out).strip(), "segments": []}


def transcribe_with_runpod_fast(audio_path: str, language: str = "nl") -> str:
    """
    Snellere transcriptie via chunking:
    - 16k mono conversie
    - micro-chunks + overlap
    - parallel /runsync calls
    - simpele stitching
    """
    if AudioSegment is None:
        raise RuntimeError("pydub niet beschikbaar. Installeer met: pip install pydub")
    
    wav16 = load_as_wav16k_mono(audio_path)

    # Maak overlappende vensters
    step = CHUNK_MS - OVERLAP_MS
    total = len(wav16)
    n_chunks = max(1, math.ceil((total - OVERLAP_MS) / step))

    jobs = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as ex:
        for i in range(n_chunks):
            start = i * step
            end = min(start + CHUNK_MS, total)
            seg = wav16[start:end]
            jobs.append(ex.submit(transcribe_chunk, seg, language))

        # resultaat in oorspronkelijke volgorde
        pieces = []
        for fut in jobs:
            res = fut.result()
            pieces.append(res["text"])

    # Eenvoudige stitching: knip duplicatie op overlap weg door laatste 10 woorden te dedupen
    stitched = []
    last_tail = ""
    for piece in pieces:
        if not piece:
            continue
        # dedupe simpele overlap
        tail_words = " ".join(last_tail.split()[-10:]) if last_tail else ""
        if tail_words and piece.startswith(tail_words):
            piece = piece[len(tail_words):].lstrip()
        stitched.append(piece)
        last_tail = piece

    return " ".join(stitched).strip()


def transcribe_with_runpod_single(audio_path, language: str = 'nl'):
    """Oudere single-shot API (langere latency, maar voor kleine bestanden nog steeds nuttig)."""
    with open(audio_path, "rb") as f:
        audio_b64 = base64.b64encode(f.read()).decode("utf-8")

    # Gebruik minimale payload - dit werkt wel!
    payload = {
        "input": {
            "audio_base64": audio_b64,
            "model": RUNPOD_MODEL,
            "language": language,   
            # Geen extra parameters - die veroorzaken problemen
        }
    }

    out = _runsync(payload)
    
    # Debug logging
    app.logger.info(f"Single shot transcribe raw output: {out}")
    
    if isinstance(out, dict):
        # Check voor FAILED status eerst
        if out.get("status") == "FAILED":
            error_msg = out.get("error", "Unknown error")
            app.logger.error(f"RunPod single shot job failed: {error_msg}")
            if "InvalidDataError" in error_msg:
                return "[RunPod Error: Audio format niet ondersteund - probeer een ander bestand]"
            else:
                return f"[RunPod Error: Job failed - {error_msg[:100]}...]"
        
        # Direct transcriptie veld (hoogste prioriteit)
        if out.get("transcription"): 
            return out["transcription"].strip()
        
        # Voor RunPod responses met output veld
        if "output" in out and isinstance(out["output"], dict):
            output = out["output"]
            transcription = output.get("transcription", "")
            return str(transcription).strip()
        
        # Fallback naar andere formaten
        if out.get("text"):          
            return out["text"].strip()
        if out.get("joined_text"):
            return out["joined_text"].strip()
        if out.get("segments"):      
            return " ".join(s.get("text","") for s in out["segments"]).strip()
        
        # Als het een status response is zonder transcriptie data
        if out.get("status") and not any(key in out for key in ["transcription", "text", "output", "segments"]):
            app.logger.error(f"RunPod single shot retourneert alleen metadata: {out}")
            return f"[RunPod Error: Alleen metadata ontvangen - {out.get('status')}]"
    
    return str(out).strip()


def transcribe_with_runpod(audio_path, language: str = 'auto'):
    """
    Kiest automatisch tussen fast chunking of single-shot op basis van bestandsgrootte.
    Voor bestanden > 5MB of > 30s: gebruik chunking
    Voor kleinere bestanden: gebruik single-shot
    """
    try:
        file_size = os.path.getsize(audio_path)
        # Voor grote bestanden (>5MB) gebruik chunking
        if file_size > 5 * 1024 * 1024:  # 5MB
            app.logger.info(f"Groot bestand ({file_size/1024/1024:.1f}MB), gebruik chunking")
            return transcribe_with_runpod_fast(audio_path, language)
        
        # Voor kleinere bestanden probeer eerst chunking (voor consistentie)
        # maar val terug op single-shot bij problemen
        try:
            return transcribe_with_runpod_fast(audio_path, language)
        except Exception as e:
            app.logger.warning(f"Chunking gefaald ({str(e)}), probeer single-shot")
            return transcribe_with_runpod_single(audio_path, language)
            
    except Exception as e:
        app.logger.error(f"RunPod transcriptie fout: {str(e)}")
        raise


# =========================
#   ROUTE HELPERS
# =========================
def get_transcription_function(language_override: str = None):
    """
    Kiest de juiste transcribe-functie o.b.v. MODEL_TYPE.
    language_override wordt enkel doorgegeven aan Whisper/RunPod.
    """
    mt = (MODEL_TYPE or "").lower()
    if mt == 'wav2vec2':
        return lambda p: transcribe_with_wav2vec2(p)
    if mt == 'runpod':
        return lambda p: transcribe_with_runpod(p, language=(language_override or 'nl'))  # Force nl instead of auto
    # default -> whisper
    return lambda p: transcribe_with_whisper(p, language=(language_override or 'nl'))


# =========================
#   ROUTES
# =========================
@app.route('/')
def index():
    return render_template('index.html')


@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'current_model_type': MODEL_TYPE,
        'whisper_model_loaded': whisper_model is not None,
        'wav2vec2_model_loaded': wav2vec2_model is not None and wav2vec2_processor is not None,
        'whisper_model_size': WHISPER_MODEL_SIZE,
        'wav2vec2_model_id': WAV2VEC2_MODEL_ID,
        'runpod_configured': bool(RUNPOD_API_KEY and RUNPOD_ENDPOINT_ID),
        'runpod_endpoint': f"{RUNPOD_BASE_URL}/{RUNPOD_ENDPOINT_ID}/runsync" if RUNPOD_ENDPOINT_ID else None,
        'chunking_config': {
            'chunk_ms': CHUNK_MS,
            'overlap_ms': OVERLAP_MS,
            'max_workers': MAX_WORKERS,
            'timeout': RUNPOD_TIMEOUT
        },
        'pydub_available': AudioSegment is not None
    })


@app.route('/transcribe', methods=['POST'])
def transcribe():
    return _do_transcription(force_chunking=False)


@app.route('/transcribe_chunked', methods=['POST'])
def transcribe_chunked():
    """Forceer chunking-gebaseerde transcriptie voor RunPod."""
    return _do_transcription(force_chunking=True)


def _do_transcription(force_chunking: bool = False):
    try:
        if 'audio' not in request.files:
            return jsonify({'error': 'Geen audiobestand gevonden'}), 400

        file = request.files['audio']
        if file.filename == '':
            return jsonify({'error': 'Geen bestand geselecteerd'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': 'Bestandstype niet ondersteund. Gebruik .wav, .mp3, .m4a, .flac, .aac, .webm of .ogg'}), 400

        # optionele taal via form-data of querystring (?language=nl|en|auto) - default nl
        language = request.form.get('language') or request.args.get('language') or 'nl'

        # Safe filename (optioneel te loggen, we gebruiken temp file)
        _ = secure_filename(file.filename)

        # Tijdelijk opslaan
        with tempfile.NamedTemporaryFile(delete=False, suffix='.audio') as temp_file:
            temp_path = temp_file.name
            file.save(temp_path)

        try:
            total_start = time.time()
            
            # Speciale logica voor RunPod chunking
            if MODEL_TYPE.lower() == 'runpod' and force_chunking:
                app.logger.info("Forceer chunking voor RunPod transcriptie")
                transcription = transcribe_with_runpod_fast(temp_path, language=(language or 'nl'))
                method_used = "runpod_chunked"
            else:
                transcribe_func = get_transcription_function(language_override=language)
                transcription = transcribe_func(temp_path)
                method_used = MODEL_TYPE
            
            total_time = time.time() - total_start

            return jsonify({
                'success': True,
                'transcription': transcription,
                'model_type': MODEL_TYPE,
                'method_used': method_used,
                'language': language,
                'timing': {'total_processing_time': round(total_time, 2)},
                'chunking_enabled': force_chunking and MODEL_TYPE.lower() == 'runpod'
            })
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except Exception as e:
        app.logger.error(f"Transcription error: {str(e)}")
        return jsonify({'error': f'Transcriptie fout: {str(e)}'}), 500


@app.route('/generate', methods=['POST'])
def generate_report():
    try:
        if not GROQ_API_KEY:
            return jsonify({'error': 'GROQ_API_KEY niet geconfigureerd'}), 500

        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': 'Geen tekst gevonden in request'}), 400

        transcription = data['text']
        system_prompt, user_prompt_template = load_prompts()

        headers = {
            'Authorization': f'Bearer {GROQ_API_KEY}',
            'Content-Type': 'application/json'
        }
        payload = {
            "model": GROQ_MODEL,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt_template.format(transcription=transcription)}
            ],
            "temperature": 0.1,
            "max_tokens": 1500
        }

        groq_start = time.time()
        response = requests.post(GROQ_URL, headers=headers, json=payload, timeout=120)
        groq_time = time.time() - groq_start

        if response.status_code == 200:
            groq_response = response.json()
            report = groq_response['choices'][0]['message']['content']
            cleaned_report = strip_think_blocks(report)
            return jsonify({
                'success': True,
                'report': cleaned_report,
                'timing': {'groq_processing_time': round(groq_time, 2)}
            })
        else:
            app.logger.error(f"Groq API error: {response.status_code} - {response.text}")
            return jsonify({'error': f'Groq API fout: {response.status_code}'}), 500

    except Exception as e:
        app.logger.error(f"Report generation error: {str(e)}")
        return jsonify({'error': f'Rapport generatie fout: {str(e)}'}), 500


@app.route('/switch_model', methods=['POST'])
def switch_model():
    """Switch between 'runpod', 'whisper' and 'wav2vec2'."""
    global MODEL_TYPE
    try:
        data = request.get_json()
        if not data or 'model_type' not in data:
            return jsonify({'error': 'model_type is required'}), 400

        new_model_type = (data['model_type'] or '').lower()
        if new_model_type not in ['whisper', 'wav2vec2', 'runpod']:
            return jsonify({'error': 'model_type must be "whisper", "wav2vec2" of "runpod"'}), 400

        MODEL_TYPE = new_model_type
        return jsonify({
            'success': True,
            'current_model_type': MODEL_TYPE,
            'message': f'Successfully switched to {MODEL_TYPE} model'
        })
    except Exception as e:
        app.logger.error(f"Model switch error: {str(e)}")
        return jsonify({'error': f'Model switch error: {str(e)}'}), 500


@app.route('/download_models', methods=['POST'])
def download_models():
    """Pre-download lokale modellen (heeft geen effect voor RunPod)."""
    results = {}
    try:
        # Whisper
        try:
            if MODEL_TYPE != 'runpod':
                app.logger.info("Pre-downloading Whisper model...")
                get_whisper_model()
                results['whisper'] = 'Downloaded successfully'
            else:
                results['whisper'] = 'Skipped (MODEL_TYPE=runpod)'
        except Exception as e:
            results['whisper'] = f'Error: {str(e)}'

        # Wav2Vec2
        try:
            if MODEL_TYPE != 'runpod':
                app.logger.info("Pre-downloading Wav2Vec2 model...")
                get_wav2vec2_model()
                results['wav2vec2'] = 'Downloaded successfully'
            else:
                results['wav2vec2'] = 'Skipped (MODEL_TYPE=runpod)'
        except Exception as e:
            results['wav2vec2'] = f'Error: {str(e)}'

        return jsonify({'success': True, 'results': results})
    except Exception as e:
        app.logger.error(f"Model download error: {str(e)}")
        return jsonify({'error': f'Model download error: {str(e)}'}), 500


# =========================
#   WEBSOCKET EVENTS VOOR STREAMING
# =========================
@socketio.on('start_streaming')
def handle_start_streaming(data):
    """Start een nieuwe streaming transcriptie sessie."""
    session_id = data.get('session_id', f"session_{int(time.time())}")
    language = data.get('language', 'nl')  # Default always Dutch
    
    # Cleanup oude sessies (>5 minuten inactief)
    current_time = time.time()
    to_remove = []
    for sid, session in _streaming_sessions.items():
        if current_time - session.last_activity > 300:  # 5 minuten
            to_remove.append(sid)
    
    for sid in to_remove:
        del _streaming_sessions[sid]
    
    # Maak nieuwe sessie
    _streaming_sessions[session_id] = StreamingSession(session_id, language)
    
    emit('streaming_started', {
        'session_id': session_id,
        'status': 'ready',
        'language': language
    })
    
    app.logger.info(f"Started streaming session: {session_id}")


@socketio.on('audio_chunk')
def handle_audio_chunk(data):
    """Verwerk een audio chunk voor streaming transcriptie."""
    session_id = data.get('session_id')
    audio_data = data.get('audio_data')  # base64 encoded
    client_sid = request.sid  # Capture the session ID here

    if not session_id or session_id not in _streaming_sessions:
        emit('error', {'message': 'Invalid session'})
        return

    session = _streaming_sessions[session_id]

    try:
        # Decode base64 audio data
        audio_bytes = base64.b64decode(audio_data)
        app.logger.info(f"Received audio chunk: {len(audio_bytes)} bytes for session {session_id}")

        # Voeg toe aan sessie buffer
        chunk_info = session.add_audio_data(audio_bytes)
        
        if chunk_info and MODEL_TYPE.lower() == 'runpod':
            # Start transcriptie in achtergrond
            def transcribe_async():
                try:
                    result = transcribe_chunk_from_bytes(
                        chunk_info['audio_data'],
                        session.language
                    )

                    # Check if we got meaningful transcription
                    transcribed_text = result['text'].strip()
                    app.logger.info(f"Transcription result for chunk {chunk_info['chunk_id']}: '{transcribed_text}'")

                    # Skip chunks that only contain asterisks or are too short
                    if transcribed_text and transcribed_text != '***' and len(transcribed_text) > 1:
                        # Stuur resultaat terug naar client
                        socketio.emit('transcription_result', {
                            'session_id': session_id,
                            'chunk_id': chunk_info['chunk_id'],
                            'text': transcribed_text,
                            'partial': True,
                            'segments': result.get('segments', [])
                        }, room=client_sid)

                        # Voeg toe aan sessie geschiedenis
                        session.transcribed_chunks.append({
                            'chunk_id': chunk_info['chunk_id'],
                            'text': transcribed_text,
                            'timestamp': time.time()
                        })
                    else:
                        app.logger.info(f"Skipping chunk {chunk_info['chunk_id']} - no speech detected ('{transcribed_text}')")
                        # Check no_speech_prob if available
                        segments = result.get('segments', [])
                        if segments and 'no_speech_prob' in segments[0]:
                            no_speech_prob = segments[0]['no_speech_prob']
                            app.logger.info(f"No speech probability: {no_speech_prob}")

                    # Note: Only meaningful chunks are added to session history above
                    
                except Exception as e:
                    app.logger.error(f"Streaming transcription error: {str(e)}")
                    socketio.emit('transcription_error', {
                        'session_id': session_id,
                        'chunk_id': chunk_info['chunk_id'],
                        'error': str(e)
                    }, room=client_sid)
            
            # Start transcriptie in thread
            import threading
            thread = threading.Thread(target=transcribe_async)
            thread.daemon = True
            thread.start()
    
    except Exception as e:
        app.logger.error(f"Audio chunk processing error: {str(e)}")
        emit('error', {'message': f'Audio processing error: {str(e)}'})


@socketio.on('stop_streaming')
def handle_stop_streaming(data):
    """Stop streaming sessie en geef finale transcriptie."""
    session_id = data.get('session_id')
    
    if not session_id or session_id not in _streaming_sessions:
        emit('error', {'message': 'Invalid session'})
        return
    
    session = _streaming_sessions[session_id]
    
    try:
        # Finaliseer de sessie
        session.finalize()
        
        # Combineer alle chunks tot finale transcriptie
        full_text = " ".join([chunk['text'] for chunk in session.transcribed_chunks])
        
        emit('streaming_complete', {
            'session_id': session_id,
            'full_transcription': full_text.strip(),
            'chunk_count': len(session.transcribed_chunks),
            'chunks': session.transcribed_chunks
        })
        
        # Cleanup sessie
        del _streaming_sessions[session_id]
        app.logger.info(f"Completed streaming session: {session_id}")
        
    except Exception as e:
        app.logger.error(f"Streaming completion error: {str(e)}")
        emit('error', {'message': f'Completion error: {str(e)}'})


@socketio.on('get_streaming_status')
def handle_get_streaming_status():
    """Geef status van alle actieve streaming sessies."""
    status = {
        'active_sessions': len(_streaming_sessions),
        'model_type': MODEL_TYPE,
        'streaming_enabled': MODEL_TYPE.lower() == 'runpod' and AudioSegment is not None
    }
    emit('streaming_status', status)


# =========================
#   MAIN
# =========================
if __name__ == '__main__':
    print("🚀 Starting Transcription + Groq Application...")
    print("⚡ Ready on port 8932")
    print(f"🤖 Current model type: {MODEL_TYPE}")
    if MODEL_TYPE.lower() == 'whisper':
        print(f"🎤 Whisper model ({WHISPER_MODEL_SIZE}) loads on first use")
    elif MODEL_TYPE.lower() == 'wav2vec2':
        print(f"🎤 Wav2Vec2 model ({WAV2VEC2_MODEL_ID}) loads on first use")
    else:
        print(f"🌐 RunPod endpoint: {RUNPOD_BASE_URL}/{RUNPOD_ENDPOINT_ID}/runsync")
        print(f"🧠 RunPod model: {RUNPOD_MODEL}, transcription: {RUNPOD_TRANSCRIPTION}, translate: {RUNPOD_TRANSLATE}, word_ts: {RUNPOD_WORD_TS}")
        print(f"⚡ Chunking: {CHUNK_MS}ms chunks, {OVERLAP_MS}ms overlap, {MAX_WORKERS} workers, {RUNPOD_TIMEOUT}s timeout")
        print(f"🎵 PyDub beschikbaar: {AudioSegment is not None}")

    print("📚 /download_models prefetches local models (whisper/wav2vec2).")
    print("🔄 /switch_model to switch between runpod/whisper/wav2vec2.")
    print("🚀 /transcribe_chunked for forced chunked transcription (RunPod only).")
    print("🎙️  WebSocket streaming endpoints for real-time transcription.")
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    socketio.run(app, host='0.0.0.0', port=8932, debug=debug_mode)
