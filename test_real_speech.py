#!/usr/bin/env python3
"""
Test script to validate the streaming transcription fix with real speech audio
"""
import base64
import io
import tempfile
import os
import time
from pydub import AudioSegment
from pydub.generators import Sine

# Import the app functions
from app import transcribe_chunk_from_bytes, transcribe_chunk, audiosegment_to_b64_wav

def create_speech_simulation():
    """Create a more realistic speech simulation with varying frequencies and pauses"""
    # Create speech-like patterns with different frequencies and durations
    segments = []
    
    # Simulate phonemes with different frequency patterns
    phonemes = [
        (200, 150),   # Low vowel sound
        (800, 100),   # Mid consonant
        (1200, 120),  # High vowel
        (400, 80),    # Mid-low consonant
        (1600, 140),  # High consonant
        (600, 160),   # Mid vowel
    ]
    
    for freq, duration in phonemes:
        # Create the tone
        tone = Sine(freq).to_audio_segment(duration=duration)
        # Add some volume variation to make it more speech-like
        tone = tone + (10 - len(segments) * 2)  # Vary volume
        segments.append(tone)
        
        # Add short pause between phonemes
        pause = AudioSegment.silent(duration=50)
        segments.append(pause)
    
    # Combine all segments
    speech_like = sum(segments)
    
    # Add longer pauses to simulate word boundaries
    word_pause = AudioSegment.silent(duration=200)
    full_speech = speech_like + word_pause + speech_like + word_pause + speech_like
    
    # Set proper audio format
    full_speech = full_speech.set_frame_rate(16000).set_channels(1)
    
    # Increase volume to ensure it's above silence threshold
    full_speech = full_speech + 15  # Increase volume significantly
    
    return full_speech

def test_speech_simulation():
    """Test with speech simulation"""
    print("=== Testing with speech simulation ===")
    
    # Create speech-like audio
    audio = create_speech_simulation()
    print(f"Created speech simulation: {len(audio)}ms, {audio.frame_rate}Hz, {audio.channels} channels")
    
    # Calculate audio level to verify it's not silent
    samples = audio.get_array_of_samples()
    avg_level = sum(abs(sample) for sample in samples) / len(samples)
    print(f"Average audio level: {avg_level}")
    
    # Test transcription
    try:
        result = transcribe_chunk(audio, "nl")
        print(f"Transcription result: {result}")
        print(f"Text: '{result.get('text', 'NO TEXT')}'")
        print(f"Text length: {len(result.get('text', ''))}")
        
        # Check no_speech_prob
        segments = result.get('segments', [])
        if segments:
            no_speech_prob = segments[0].get('no_speech_prob', 0)
            print(f"No speech probability: {no_speech_prob}")
            
        return result.get('text', '') != '***'
        
    except Exception as e:
        print(f"Error in transcribe_chunk: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_audio_file():
    """Test with a real audio file if available"""
    print("\n=== Testing with real audio file ===")
    
    # Look for any audio files in the current directory
    audio_files = []
    for ext in ['wav', 'mp3', 'm4a', 'flac', 'ogg']:
        audio_files.extend([f for f in os.listdir('.') if f.lower().endswith(f'.{ext}')])
    
    if not audio_files:
        print("No audio files found in current directory")
        return False
    
    audio_file = audio_files[0]
    print(f"Testing with audio file: {audio_file}")
    
    try:
        # Load the audio file
        audio = AudioSegment.from_file(audio_file)
        
        # Convert to proper format
        audio = audio.set_frame_rate(16000).set_channels(1)
        
        # Take first 3 seconds to match our chunk size
        audio = audio[:3000]
        
        print(f"Loaded audio: {len(audio)}ms, {audio.frame_rate}Hz, {audio.channels} channels")
        
        # Calculate audio level
        samples = audio.get_array_of_samples()
        avg_level = sum(abs(sample) for sample in samples) / len(samples)
        print(f"Average audio level: {avg_level}")
        
        # Test transcription
        result = transcribe_chunk(audio, "nl")
        print(f"Transcription result: {result}")
        print(f"Text: '{result.get('text', 'NO TEXT')}'")
        
        # Check no_speech_prob
        segments = result.get('segments', [])
        if segments:
            no_speech_prob = segments[0].get('no_speech_prob', 0)
            print(f"No speech probability: {no_speech_prob}")
            
        return result.get('text', '') != '***'
        
    except Exception as e:
        print(f"Error testing with real audio file: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chunk_filtering():
    """Test the new chunk filtering logic"""
    print("\n=== Testing chunk filtering logic ===")
    
    # Test with silent audio (should be filtered out)
    silent_audio = AudioSegment.silent(duration=3000).set_frame_rate(16000).set_channels(1)
    print("Testing silent audio...")
    
    try:
        result = transcribe_chunk(silent_audio, "nl")
        text = result.get('text', '')
        print(f"Silent audio result: '{text}'")
        
        if text == '***':
            print("✓ Silent audio correctly returns *** (will be filtered)")
        else:
            print(f"✗ Unexpected result for silent audio: '{text}'")
            
    except Exception as e:
        print(f"Error testing silent audio: {e}")

if __name__ == "__main__":
    print("Starting comprehensive streaming transcription tests...")
    print("=" * 60)
    
    # Test 1: Speech simulation
    speech_success = test_speech_simulation()
    
    # Test 2: Real audio file (if available)
    real_audio_success = test_with_real_audio_file()
    
    # Test 3: Chunk filtering
    test_chunk_filtering()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY:")
    print(f"Speech simulation: {'✓ PASS' if speech_success else '✗ FAIL'}")
    print(f"Real audio file: {'✓ PASS' if real_audio_success else '✗ SKIP/FAIL'}")
    
    if speech_success or real_audio_success:
        print("\n🎉 SUCCESS: At least one test produced meaningful transcription!")
        print("The streaming transcription should now work better with:")
        print("- Longer 3-second chunks for better speech detection")
        print("- Audio level filtering to skip silent chunks")
        print("- Improved logging for debugging")
    else:
        print("\n⚠️  All tests still return *** - this suggests:")
        print("- The RunPod model may be very sensitive to audio quality")
        print("- Real human speech may be needed for proper transcription")
        print("- The model might need actual spoken words in Dutch")
    
    print("\nNext steps:")
    print("1. Test with the web interface using real microphone input")
    print("2. Check browser console logs for audio level information")
    print("3. Verify microphone permissions and audio capture")
