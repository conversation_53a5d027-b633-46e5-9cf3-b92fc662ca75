../../../bin/get_gprof,sha256=zoGJZ9XUIwb6oscXHIJjlHmiDyyupsl6ktCo-WRpqyU,2527
../../../bin/get_objgraph,sha256=XUZScfUFwTMkBgtTNKO7_N_4aOIOi-nv-tgEwWjmR7o,1721
../../../bin/undill,sha256=wqLdvNoXaNT3JYt8vPqQ8x-Mws01y-HidjM-VtW4gKY,657
dill-0.3.8.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dill-0.3.8.dist-info/LICENSE,sha256=UeiKI-eId86r1yfCGcel4z9l2pugOsT9KFupBKoc4is,1790
dill-0.3.8.dist-info/METADATA,sha256=UxkSs2cU8JyrJsV5kS0QR9crJ07hrUJS2RiIMQaC4ss,10106
dill-0.3.8.dist-info/RECORD,,
dill-0.3.8.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
dill-0.3.8.dist-info/top_level.txt,sha256=HLSIyYIjQzJiBvs3_-16ntezE3j6mWGTW0DT1xDd7X0,5
dill/__diff.py,sha256=kirMxzB7E8lfjo21M5oIf7if95ny0aWhYB790KMpN08,7143
dill/__info__.py,sha256=Kmel_yLTyH-hwNC5cVfzN-LV08AbS_AvSa2uwMeIQdk,10756
dill/__init__.py,sha256=j-Jxl3H6bxatS0h2f8ywWs7DChwk7B9ozuZQBVcjYGU,3798
dill/__pycache__/__diff.cpython-310.pyc,,
dill/__pycache__/__info__.cpython-310.pyc,,
dill/__pycache__/__init__.cpython-310.pyc,,
dill/__pycache__/_dill.cpython-310.pyc,,
dill/__pycache__/_objects.cpython-310.pyc,,
dill/__pycache__/_shims.cpython-310.pyc,,
dill/__pycache__/detect.cpython-310.pyc,,
dill/__pycache__/logger.cpython-310.pyc,,
dill/__pycache__/objtypes.cpython-310.pyc,,
dill/__pycache__/pointers.cpython-310.pyc,,
dill/__pycache__/session.cpython-310.pyc,,
dill/__pycache__/settings.cpython-310.pyc,,
dill/__pycache__/source.cpython-310.pyc,,
dill/__pycache__/temp.cpython-310.pyc,,
dill/_dill.py,sha256=3Eo6gKj1sODJjgPgYNT8TU-YL6QNQ7rIeWPUVnRzyqQ,88548
dill/_objects.py,sha256=dPlUXzQIh8CA0fMy9NMbwwLGUPmXe5H8MdQtRWB1b_M,19605
dill/_shims.py,sha256=IuzQcyPET5VWmWMoSGStieoedvNXlb5suDpa4bykTbQ,6635
dill/detect.py,sha256=Mb-PfCxn1mg0l3TmHXyPNVEc4n3fuxc_nue6eL3-q_o,11114
dill/logger.py,sha256=YS5ZloAOKjJRZaOBRCaMUDWmWVQZcicvbXVSrz8L8XU,11134
dill/objtypes.py,sha256=BamGH3BEM6lLlxisuvXcGjsCRLNeoLs4_rFZrM5r2yM,736
dill/pointers.py,sha256=vnQzjwGtKMGnmbdYRXRWNLMyceNPSw4f7UpvwCXLYbE,4467
dill/session.py,sha256=NvCWpoP9r_rGBL2pOwwxOri8mFly5KlIWG3GwkBFnc0,23525
dill/settings.py,sha256=7I3yvSpPKstOqpoW2gv3X77kXK-hZlqCnF7nJUGhxTY,630
dill/source.py,sha256=DWfIxcBjpjbbKYz2DstV9kRdjajBdZLOcLXfsZsPo9U,45121
dill/temp.py,sha256=KJUry4t0UjQCh5t4LXcxNyMF_uOGHwcjTuNYTJD9qdA,8027
dill/tests/__init__.py,sha256=Gx-chVB-l-e7ncsGp2zF4BimTjbUyO7BY7RkrO835vY,479
dill/tests/__main__.py,sha256=fHhioQwcOvTPlf1RM_wVQ0Y3ndETWJOuXJQ2rVtqliA,899
dill/tests/__pycache__/__init__.cpython-310.pyc,,
dill/tests/__pycache__/__main__.cpython-310.pyc,,
dill/tests/__pycache__/test_abc.cpython-310.pyc,,
dill/tests/__pycache__/test_check.cpython-310.pyc,,
dill/tests/__pycache__/test_classdef.cpython-310.pyc,,
dill/tests/__pycache__/test_dataclasses.cpython-310.pyc,,
dill/tests/__pycache__/test_detect.cpython-310.pyc,,
dill/tests/__pycache__/test_dictviews.cpython-310.pyc,,
dill/tests/__pycache__/test_diff.cpython-310.pyc,,
dill/tests/__pycache__/test_extendpickle.cpython-310.pyc,,
dill/tests/__pycache__/test_fglobals.cpython-310.pyc,,
dill/tests/__pycache__/test_file.cpython-310.pyc,,
dill/tests/__pycache__/test_functions.cpython-310.pyc,,
dill/tests/__pycache__/test_functors.cpython-310.pyc,,
dill/tests/__pycache__/test_logger.cpython-310.pyc,,
dill/tests/__pycache__/test_mixins.cpython-310.pyc,,
dill/tests/__pycache__/test_module.cpython-310.pyc,,
dill/tests/__pycache__/test_moduledict.cpython-310.pyc,,
dill/tests/__pycache__/test_nested.cpython-310.pyc,,
dill/tests/__pycache__/test_objects.cpython-310.pyc,,
dill/tests/__pycache__/test_properties.cpython-310.pyc,,
dill/tests/__pycache__/test_pycapsule.cpython-310.pyc,,
dill/tests/__pycache__/test_recursive.cpython-310.pyc,,
dill/tests/__pycache__/test_registered.cpython-310.pyc,,
dill/tests/__pycache__/test_restricted.cpython-310.pyc,,
dill/tests/__pycache__/test_selected.cpython-310.pyc,,
dill/tests/__pycache__/test_session.cpython-310.pyc,,
dill/tests/__pycache__/test_source.cpython-310.pyc,,
dill/tests/__pycache__/test_temp.cpython-310.pyc,,
dill/tests/__pycache__/test_weakref.cpython-310.pyc,,
dill/tests/test_abc.py,sha256=BSjSKKCQ5_iPfFxAd0yBq4KSAJxelrlC3IzoAhjd1C4,4227
dill/tests/test_check.py,sha256=4F5gkX6zxY7C5sD2_0Tkqf3T3jmQl0K15FOxYUTZQl0,1396
dill/tests/test_classdef.py,sha256=fI3fVk4SlsjNMMs5RfU6DUCaxpP7YYRjvLZ2nhXMHuc,8600
dill/tests/test_dataclasses.py,sha256=yKjFuG24ymLtjk-sZZdhvNY7aDqerTDpMcfi_eV4ft0,890
dill/tests/test_detect.py,sha256=sE9THufHXCDysBPQ4QkN5DHn6DaIldVRAEciseIRH08,4083
dill/tests/test_dictviews.py,sha256=Jhol0cQWPwoQrp7OPxGhU8FNRX2GgfFp9fTahCvQEPA,1337
dill/tests/test_diff.py,sha256=5VIWf2fpV6auLHNfzkHLTrgx6AJBlE2xe5Wanfmq8TM,2667
dill/tests/test_extendpickle.py,sha256=gONrMBHO94Edhnqm1wo49hgzwmaxHs7L-86Hs-7albY,1315
dill/tests/test_fglobals.py,sha256=DCvdojmKcLN_X9vX4Qe1FbsqjeoJK-wsY2uJwBfNFro,1676
dill/tests/test_file.py,sha256=jUU2h8qaDOIe1mn_Ng7wqCZcd7Ucx3TAaI-K_90_Tbk,13578
dill/tests/test_functions.py,sha256=-mqTpUbzRu8GynjBGD25dRDm8qInIe07sRZmCcA_iXY,4267
dill/tests/test_functors.py,sha256=7rx9wLmrgFwF0gUm_-SGOISPYSok0XjmrQ-jFMRt6gs,930
dill/tests/test_logger.py,sha256=D9zGRaA-CEadG13orPS_D4gPVZlkqXf9Zu8wn2oMiYc,2385
dill/tests/test_mixins.py,sha256=YtB24BjodooLj85ijFbAxiM7LlFQZAUL8RQVx9vIAwY,4007
dill/tests/test_module.py,sha256=KLl_gZJJqDY7S_bD5wCqKL8JQCS0MDMoipVQSDfASlo,1943
dill/tests/test_moduledict.py,sha256=faXG6-5AcmCfP3xe2FYGOUdSosU-9TWnKU_ZVqPDaxY,1182
dill/tests/test_nested.py,sha256=ViWiOrChLZktS0z6qyKqMxDdTuy9kAX4qMgH_OreMcc,3146
dill/tests/test_objects.py,sha256=pPAth0toC_UWztuKHC7NZlsRBb0g_gSAt70UbUtXEXo,1931
dill/tests/test_properties.py,sha256=h35c-lYir1JG6oLPtrA0eYE0xoSohIimsA3yIfRw6yA,1346
dill/tests/test_pycapsule.py,sha256=EXFyB6g1Wx9O9LM6StIeUKhrhln4_hou1xrtGwkt4Cw,1417
dill/tests/test_recursive.py,sha256=bfr-BsK1Xu0PU7l2srHsDXdY2l1LeM3L3w7NraXO0cc,4182
dill/tests/test_registered.py,sha256=J3oku053VfdJgYh4Z5_kyFRf-C52JglIzjcyxEaYOhk,1573
dill/tests/test_restricted.py,sha256=xLMIae8sYJksAj9hKKyHFHIL8vtbGpFeOULz59snYM4,783
dill/tests/test_selected.py,sha256=Hp-AAd6Qp5FJZ-vY_Bbejo5Rg6xFstec5QkSg5D7Aac,3218
dill/tests/test_session.py,sha256=KoSPvs4c4VJ8mFMF7EUlD_3GwcOhhipt9fqHr--Go-4,10161
dill/tests/test_source.py,sha256=wZTYBbpzUwj3Mz5OjrHQKfskaVVwuy2UQDg5p2wLbT4,6036
dill/tests/test_temp.py,sha256=F_7nJkSetLIBSAYMw1-hYh03iVrEYwGs-4GIUzoBOfY,2619
dill/tests/test_weakref.py,sha256=mrjZP5aPtUP1wBD6ibPsDsfI9ffmq_Ykt7ltoodi5Lg,1602
