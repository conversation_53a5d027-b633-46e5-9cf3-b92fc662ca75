
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-03-18T15:33:58-0400",
 "dirty": false,
 "error": null,
 "full-revisionid": "47b445ae4c284a82dd15e0287b1ffc410e8fc470",
 "version": "2024.3.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
