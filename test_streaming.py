#!/usr/bin/env python3
"""
Test script to simulate streaming transcription and debug the issue
"""
import base64
import io
import tempfile
import os
from pydub import AudioSegment
from pydub.generators import Sine

# Import the app functions
from app import transcribe_chunk_from_bytes, transcribe_chunk, audiosegment_to_b64_wav

def create_test_audio():
    """Create a simple test audio segment"""
    # Generate a 2-second sine wave at 440Hz
    audio = Sine(440).to_audio_segment(duration=2000)
    audio = audio.set_frame_rate(16000).set_channels(1)
    return audio

def create_speech_like_audio():
    """Create audio that might be more speech-like"""
    # Create a mix of different frequencies to simulate speech
    base = Sine(200).to_audio_segment(duration=500)  # Low frequency
    mid = Sine(800).to_audio_segment(duration=500)   # Mid frequency
    high = Sine(1600).to_audio_segment(duration=500) # High frequency
    silence = AudioSegment.silent(duration=500)      # Silence

    # Combine them to create speech-like patterns
    speech_like = base + silence + mid + silence + high + silence + base + mid
    speech_like = speech_like.set_frame_rate(16000).set_channels(1)

    # Add some volume variation
    speech_like = speech_like + 10  # Increase volume
    return speech_like

def test_transcribe_chunk():
    """Test the transcribe_chunk function with a simple audio"""
    print("=== Testing transcribe_chunk function (sine wave) ===")

    # Create test audio
    audio = create_test_audio()
    print(f"Created test audio: {len(audio)}ms, {audio.frame_rate}Hz, {audio.channels} channels")

    # Test transcription
    try:
        result = transcribe_chunk(audio, "nl")
        print(f"Transcription result: {result}")
        print(f"Text: '{result.get('text', 'NO TEXT')}'")
        print(f"Text length: {len(result.get('text', ''))}")
        print(f"Text repr: {repr(result.get('text', ''))}")

        # Check no_speech_prob
        segments = result.get('segments', [])
        if segments:
            no_speech_prob = segments[0].get('no_speech_prob', 0)
            print(f"No speech probability: {no_speech_prob}")

    except Exception as e:
        print(f"Error in transcribe_chunk: {e}")
        import traceback
        traceback.print_exc()

def test_transcribe_chunk_speech_like():
    """Test the transcribe_chunk function with speech-like audio"""
    print("\n=== Testing transcribe_chunk function (speech-like) ===")

    # Create speech-like test audio
    audio = create_speech_like_audio()
    print(f"Created speech-like audio: {len(audio)}ms, {audio.frame_rate}Hz, {audio.channels} channels")

    # Test transcription
    try:
        result = transcribe_chunk(audio, "nl")
        print(f"Transcription result: {result}")
        print(f"Text: '{result.get('text', 'NO TEXT')}'")
        print(f"Text length: {len(result.get('text', ''))}")
        print(f"Text repr: {repr(result.get('text', ''))}")

        # Check no_speech_prob
        segments = result.get('segments', [])
        if segments:
            no_speech_prob = segments[0].get('no_speech_prob', 0)
            print(f"No speech probability: {no_speech_prob}")

    except Exception as e:
        print(f"Error in transcribe_chunk: {e}")
        import traceback
        traceback.print_exc()

def test_transcribe_chunk_from_bytes():
    """Test the transcribe_chunk_from_bytes function with WebM audio"""
    print("\n=== Testing transcribe_chunk_from_bytes function ===")
    
    # Create test audio and export as WebM
    audio = create_test_audio()
    
    # Export to WebM format
    with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
        audio.export(temp_file.name, format="webm")
        
        # Read the WebM bytes
        with open(temp_file.name, 'rb') as f:
            webm_bytes = f.read()
        
        os.unlink(temp_file.name)
    
    print(f"Created WebM audio: {len(webm_bytes)} bytes")
    
    # Test transcription
    try:
        result = transcribe_chunk_from_bytes(webm_bytes, "nl")
        print(f"Transcription result: {result}")
        print(f"Text: '{result.get('text', 'NO TEXT')}'")
        print(f"Text length: {len(result.get('text', ''))}")
        print(f"Text repr: {repr(result.get('text', ''))}")
    except Exception as e:
        print(f"Error in transcribe_chunk_from_bytes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting streaming transcription tests...")
    test_transcribe_chunk()
    test_transcribe_chunk_speech_like()
    test_transcribe_chunk_from_bytes()
    print("Tests completed.")
