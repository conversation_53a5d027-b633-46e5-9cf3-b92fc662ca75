<!DOCTYPE html>
<html lang="nl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ZorgPortaal - Spraak naar Zorgverslag</title>

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Quill Editor -->
    <link
      href="https://cdn.quilljs.com/1.3.6/quill.snow.css"
      rel="stylesheet"
    />
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <style>
      :root {
        --primary-color: #3b82f6;
        --primary-dark: #2563eb;
        --secondary-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
          0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
          0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
          0 8px 10px -6px rgb(0 0 0 / 0.1);
        --border-radius: 12px;
        --border-radius-sm: 8px;
        --border-radius-lg: 16px;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        background: linear-gradient(
          135deg,
          rgb(243, 243, 243) 0%,
          rgb(160, 160, 160) 50%,
          rgb(255, 255, 255) 100%
        );
        min-height: 100vh;
        color: var(--gray-800);
        line-height: 1.6;
      }

      .main-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        padding: 2rem;
      }

      .app-wrapper {
        max-width: 1400px;
        width: 100%;
        margin: 0 auto;
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 800px;
      }

      /* Left Panel - Controls */
      .control-panel {
        background: var(--gray-50);
        padding: 2.5rem;
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
      }

      /* Right Panel - Editor */
      .editor-panel {
        background: white;
        padding: 2.5rem;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      /* Header */
      .app-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid var(--gray-200);
      }

      .app-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
      }

      .app-subtitle {
        color: var(--gray-600);
        font-size: 1.1rem;
        font-weight: 400;
      }

      /* Cards */
      .card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        overflow: hidden;
        margin-bottom: 1.5rem;
      }

      .card-header {
        padding: 1.25rem;
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
      }

      .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .card-body {
        padding: 1.25rem;
      }

      /* Model Selection */
      .model-status {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--success-color);
        color: white;
        border-radius: var(--border-radius-sm);
        margin-bottom: 1rem;
        font-weight: 500;
      }

      .model-status.loading {
        background: var(--warning-color);
      }

      .model-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
        margin-bottom: 1rem;
      }

      .model-btn {
        padding: 0.75rem 1rem;
        border: 2px solid var(--gray-300);
        background: white;
        border-radius: var(--border-radius-sm);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
      }

      .model-btn:hover {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: white;
      }

      .model-btn.active {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: white;
      }

      .model-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      /* Upload Section */
      .upload-area {
        border: 2px dashed var(--gray-300);
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        background: var(--gray-50);
        transition: all 0.2s ease;
        cursor: pointer;
      }

      .upload-area:hover,
      .upload-area.dragover {
        border-color: var(--primary-color);
        background: #eff6ff;
      }

      .upload-icon {
        font-size: 3rem;
        color: var(--gray-400);
        margin-bottom: 1rem;
      }

      .upload-area:hover .upload-icon,
      .upload-area.dragover .upload-icon {
        color: var(--primary-color);
      }

      .upload-text {
        font-size: 1.125rem;
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
      }

      .upload-subtext {
        color: var(--gray-500);
        font-size: 0.875rem;
      }

      .file-input {
        display: none;
      }

      /* Buttons */
      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius-sm);
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-decoration: none;
        min-height: 44px;
      }

      .btn-primary {
        background: var(--primary-color);
        color: white;
      }

      .btn-primary:hover:not(:disabled) {
        background: var(--primary-dark);
        transform: translateY(-1px);
      }

      .btn-success {
        background: var(--success-color);
        color: white;
      }

      .btn-success:hover:not(:disabled) {
        background: #059669;
        transform: translateY(-1px);
      }

      .btn-secondary {
        background: var(--secondary-color);
        color: white;
      }

      .btn-secondary:hover:not(:disabled) {
        background: #0891b2;
        transform: translateY(-1px);
      }

      .btn-warning {
        background: var(--warning-color);
        color: white;
      }

      .btn-warning:hover:not(:disabled) {
        background: #d97706;
        transform: translateY(-1px);
      }

      .btn-error {
        background: var(--error-color);
        color: white;
      }

      .btn-error:hover:not(:disabled) {
        background: #dc2626;
        transform: translateY(-1px);
      }

      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
      }

      .btn-full {
        width: 100%;
      }

      /* Recording */
      .record-btn {
        font-size: 1.125rem;
        padding: 1rem 2rem;
        margin-bottom: 1rem;
      }

      .record-btn.recording {
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .recording-info {
        background: #fee2e2;
        border: 1px solid #fecaca;
        border-radius: var(--border-radius-sm);
        padding: 1rem;
        margin-top: 1rem;
        display: none;
      }

      .recording-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .recording-dot {
        width: 12px;
        height: 12px;
        background: var(--error-color);
        border-radius: 50%;
        animation: blink 1s infinite;
      }

      @keyframes blink {
        0%,
        50% {
          opacity: 1;
        }
        51%,
        100% {
          opacity: 0.3;
        }
      }

      .recording-time {
        font-family: "Courier New", monospace;
        font-weight: 700;
        font-size: 1.25rem;
        color: var(--error-color);
      }

      .recording-level {
        width: 100%;
        height: 8px;
        background: var(--gray-200);
        border-radius: 4px;
        overflow: hidden;
      }

      .level-bar {
        height: 100%;
        background: linear-gradient(
          90deg,
          var(--success-color) 0%,
          var(--warning-color) 70%,
          var(--error-color) 100%
        );
        width: 0%;
        transition: width 0.1s ease;
        border-radius: 4px;
      }

      /* File Info */
      .file-info {
        background: #dbeafe;
        border: 1px solid #bfdbfe;
        border-radius: var(--border-radius-sm);
        padding: 1rem;
        margin-top: 1rem;
        display: none;
      }

      .file-info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
      }

      .file-info-item:last-child {
        margin-bottom: 0;
      }

      .file-info-label {
        font-weight: 500;
        color: var(--gray-700);
      }

      .file-info-value {
        color: var(--gray-600);
      }

      /* Editor Section */
      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--gray-200);
      }

      .editor-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .editor-actions {
        display: flex;
        gap: 0.75rem;
      }

      /* Text Editor */
      .text-editor-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--gray-200);
        border-radius: var(--border-radius);
        overflow: hidden;
      }

      .editor-toolbar {
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
        padding: 0.75rem;
      }

      .text-editor {
        flex: 1;
        min-height: 400px;
        background: white;
      }

      .text-editor .ql-editor {
        padding: 1.5rem;
        font-size: 1rem;
        line-height: 1.7;
        min-height: 400px;
      }

      .text-editor .ql-editor.ql-blank::before {
        color: var(--gray-400);
        font-style: normal;
      }

      /* Tabs */
      .tab-container {
        margin-bottom: 1rem;
      }

      .tab-nav {
        display: flex;
        background: var(--gray-100);
        border-radius: var(--border-radius-sm);
        padding: 0.25rem;
      }

      .tab-btn {
        flex: 1;
        padding: 0.75rem 1rem;
        background: transparent;
        border: none;
        border-radius: calc(var(--border-radius-sm) - 2px);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        color: var(--gray-600);
      }

      .tab-btn.active {
        background: white;
        color: var(--gray-800);
        box-shadow: var(--shadow-sm);
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      /* Loading & States */
      .loading {
        display: none;
        text-align: center;
        padding: 2rem;
      }

      .loading.show {
        display: block;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid var(--gray-200);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-text {
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
      }

      .loading-subtext {
        color: var(--gray-500);
        font-size: 0.875rem;
      }

      /* Alerts */
      .alert {
        padding: 1rem;
        border-radius: var(--border-radius-sm);
        margin-bottom: 1rem;
        display: none;
      }

      .alert.show {
        display: block;
      }

      .alert-error {
        background: #fef2f2;
        color: #991b1b;
        border: 1px solid #fecaca;
      }

      .alert-success {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .alert-info {
        background: #eff6ff;
        color: #1e40af;
        border: 1px solid #bfdbfe;
      }

      /* Timing Info */
      .timing-section {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: var(--border-radius-sm);
        padding: 1rem;
        margin-bottom: 1rem;
        display: none;
      }

      .timing-section.show {
        display: block;
      }

      .timing-title {
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .timing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
      }

      .timing-item {
        background: white;
        padding: 0.75rem;
        border-radius: var(--border-radius-sm);
        text-align: center;
      }

      .timing-label {
        font-size: 0.75rem;
        color: var(--gray-600);
        text-transform: uppercase;
        font-weight: 600;
        letter-spacing: 0.05em;
        margin-bottom: 0.25rem;
      }

      .timing-value {
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--gray-800);
      }

      /* Progress Indicator */
      .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 1.5rem 0;
        padding: 0 1rem;
      }

      .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
      }

      .progress-step:not(:last-child)::after {
        content: "";
        position: absolute;
        top: 1rem;
        left: 50%;
        width: 100%;
        height: 2px;
        background: var(--gray-300);
        z-index: 1;
      }

      .progress-step.completed:not(:last-child)::after {
        background: var(--primary-color);
      }

      .progress-circle {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background: var(--gray-300);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: white;
        position: relative;
        z-index: 2;
        margin-bottom: 0.5rem;
      }

      .progress-step.completed .progress-circle {
        background: var(--primary-color);
      }

      .progress-step.active .progress-circle {
        background: var(--warning-color);
        animation: pulse 2s infinite;
      }

      .progress-label {
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--gray-600);
        text-align: center;
      }

      .progress-step.completed .progress-label {
        color: var(--primary-color);
      }

      /* Responsive Design */
      @media (max-width: 1024px) {
        .app-wrapper {
          grid-template-columns: 1fr;
          max-width: 800px;
        }

        .editor-panel {
          border-top: 1px solid var(--gray-200);
        }

        .control-panel {
          border-right: none;
          border-bottom: 1px solid var(--gray-200);
        }
      }

      @media (max-width: 768px) {
        .main-container {
          padding: 1rem;
        }

        .control-panel,
        .editor-panel {
          padding: 1.5rem;
        }

        .app-title {
          font-size: 1.5rem;
        }

        .model-buttons {
          grid-template-columns: 1fr !important;
        }

        .editor-actions {
          flex-wrap: wrap;
        }

        .timing-grid {
          grid-template-columns: 1fr;
        }

        .progress-steps {
          flex-direction: column;
          gap: 1rem;
        }

        .progress-step:not(:last-child)::after {
          display: none;
        }
      }

      /* Custom Scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: var(--gray-100);
      }

      ::-webkit-scrollbar-thumb {
        background: var(--gray-400);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--gray-500);
      }

      /* Print Styles */
      @media print {
        .control-panel,
        .editor-actions,
        .btn {
          display: none !important;
        }

        .app-wrapper {
          grid-template-columns: 1fr;
          box-shadow: none;
        }

        .text-editor .ql-editor {
          border: none;
          padding: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="main-container">
      <div class="app-wrapper">
        <!-- Left Panel - Controls -->
        <div class="control-panel">
          <div class="app-header">
            <h1 class="app-title">
              <i class="fas fa-microphone"></i>
              ZorgPortaal
            </h1>
            <p class="app-subtitle">Spraak naar Professioneel Zorgverslag</p>
          </div>

          <!-- Progress Indicator -->
          <div class="progress-steps">
            <div class="progress-step" id="step-upload">
              <div class="progress-circle">1</div>
              <div class="progress-label">Upload</div>
            </div>
            <div class="progress-step" id="step-transcribe">
              <div class="progress-circle">2</div>
              <div class="progress-label">Transcriptie</div>
            </div>
            <div class="progress-step" id="step-generate">
              <div class="progress-circle">3</div>
              <div class="progress-label">Rapport</div>
            </div>
          </div>

          <!-- Model Selection -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-brain"></i>
                Speech-to-Text Model
              </h3>
            </div>
            <div class="card-body">
              <div class="model-status" id="modelStatus">
                <i class="fas fa-microphone"></i>
                <span id="currentModelText">Loading model information...</span>
              </div>

              <div
                class="model-buttons"
                style="grid-template-columns: 1fr 1fr 1fr"
              >
                <button class="model-btn" id="runpodBtn" data-model="runpod">
                  <i class="fas fa-cloud"></i>
                  RunPod
                </button>
                <button class="model-btn" id="whisperBtn" data-model="whisper">
                  <i class="fas fa-microphone"></i>
                  Whisper
                </button>
                <button
                  class="model-btn"
                  id="wav2vec2Btn"
                  data-model="wav2vec2"
                >
                  <i class="fas fa-volume-up"></i>
                  Wav2Vec2
                </button>
              </div>

              <button class="btn btn-secondary btn-full" id="downloadModelsBtn">
                <i class="fas fa-download"></i>
                Download Modellen
              </button>

              <div
                style="
                  font-size: 0.8rem;
                  color: var(--gray-600);
                  margin-top: 1rem;
                  line-height: 1.4;
                "
              >
                <strong>RunPod:</strong> Cloud-based Whisper, snelste
                verwerking<br />
                <strong>Whisper:</strong> Lokaal model, zeer accuraat<br />
                <strong>Wav2Vec2:</strong> Gespecialiseerd voor Nederlands
              </div>
            </div>
          </div>

          <!-- Audio Input -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-upload"></i>
                Audio Invoer
              </h3>
            </div>
            <div class="card-body">
              <!-- Recording Section -->
              <button class="btn btn-error btn-full record-btn" id="recordBtn">
                <i class="fas fa-microphone"></i>
                Start Opname
              </button>

              <button
                class="btn btn-secondary btn-full record-btn"
                id="streamRecordBtn"
                style="margin-top: 0.5rem"
              >
                <i class="fas fa-broadcast-tower"></i>
                Start Streaming Opname
              </button>

              <div class="recording-info" id="recordingInfo">
                <div class="recording-indicator">
                  <span class="recording-dot"></span>
                  <span class="recording-time" id="recordingTime">00:00</span>
                </div>
                <div class="recording-level" id="recordingLevel">
                  <div class="level-bar"></div>
                </div>
              </div>

              <div
                class="recording-info"
                id="streamingInfo"
                style="display: none"
              >
                <div class="recording-indicator">
                  <span
                    class="recording-dot"
                    style="background: var(--secondary-color)"
                  ></span>
                  <span class="recording-time" id="streamingTime">00:00</span>
                  <span
                    style="
                      margin-left: 10px;
                      font-size: 0.8rem;
                      color: var(--secondary-color);
                    "
                  >
                    🎙️ Live transcriptie
                  </span>
                </div>
                <div
                  id="streamingChunks"
                  style="
                    margin-top: 10px;
                    font-size: 0.8rem;
                    color: var(--gray-600);
                  "
                >
                  Chunks verwerkt: 0
                </div>
              </div>

              <!-- Divider -->
              <div
                style="
                  text-align: center;
                  margin: 1.5rem 0;
                  color: var(--gray-400);
                  font-weight: 500;
                "
              >
                ─── of ───
              </div>

              <!-- Upload Area -->
              <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">Sleep audiobestand hierheen</div>
                <div class="upload-subtext">of klik om te selecteren</div>
                <input
                  type="file"
                  id="audioFile"
                  class="file-input"
                  accept=".wav,.mp3,.m4a,.flac,.aac,.webm,.ogg"
                />
              </div>

              <div class="file-info" id="fileInfo"></div>

              <button
                class="btn btn-primary btn-full"
                id="transcribeBtn"
                style="display: none"
              >
                <i class="fas fa-microphone"></i>
                Transcribeer Audio
              </button>
            </div>
          </div>

          <!-- Generate Report -->
          <button
            class="btn btn-success btn-full"
            id="generateBtn"
            style="display: none"
          >
            <i class="fas fa-file-medical"></i>
            Genereer Zorgverslag
          </button>

          <!-- Loading -->
          <div class="loading" id="loadingDiv">
            <div class="spinner"></div>
            <div class="loading-text" id="loadingText">
              Bezig met verwerken...
            </div>
            <div class="loading-subtext" id="loadingSubtext"></div>
          </div>

          <!-- Alerts -->
          <div class="alert alert-error" id="errorAlert"></div>
          <div class="alert alert-success" id="successAlert"></div>

          <!-- Timing Information -->
          <div class="timing-section" id="timingSection">
            <div class="timing-title">
              <i class="fas fa-stopwatch"></i>
              Verwerkingstijden
            </div>
            <div class="timing-grid" id="timingGrid"></div>
          </div>
        </div>

        <!-- Right Panel - Editor -->
        <div class="editor-panel">
          <div class="editor-header">
            <h2 class="editor-title">
              <i class="fas fa-edit"></i>
              Document Editor
            </h2>
            <div class="editor-actions">
              <button
                class="btn btn-secondary"
                id="copyBtn"
                title="Kopieer tekst"
              >
                <i class="fas fa-copy"></i>
              </button>
              <button
                class="btn btn-secondary"
                id="printBtn"
                title="Print document"
              >
                <i class="fas fa-print"></i>
              </button>
              <button
                class="btn btn-secondary"
                id="downloadBtn"
                title="Download als Word"
              >
                <i class="fas fa-download"></i>
              </button>
              <button class="btn btn-secondary" id="clearBtn" title="Wis alles">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Tabs -->
          <div class="tab-container">
            <div class="tab-nav">
              <button class="tab-btn active" data-tab="transcription">
                <i class="fas fa-microphone"></i>
                Transcriptie
              </button>
              <button class="tab-btn" data-tab="report">
                <i class="fas fa-file-medical"></i>
                Zorgverslag
              </button>
            </div>
          </div>

          <!-- Text Editor -->
          <div class="text-editor-container">
            <div class="text-editor" id="textEditor">
              <!-- Quill editor will be initialized here -->
            </div>
          </div>

          <!-- Tab Contents (hidden containers for data) -->
          <div class="tab-content active" id="transcription-content"></div>
          <div class="tab-content" id="report-content"></div>
        </div>
      </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script>
      // Global variables
      let selectedFile = null;
      let currentTranscription = null;
      let currentReport = null;
      let mediaRecorder = null;
      let audioChunks = [];
      let recordingStartTime = null;
      let recordingInterval = null;
      let audioContext = null;
      let analyser = null;
      let microphone = null;
      let timingData = {};
      let currentModel = null;
      let activeTab = "transcription";

      // Streaming variables
      let socket = null;
      let isStreaming = false;
      let streamingSession = null;
      let streamingStartTime = null;
      let streamingInterval = null;
      let streamingChunkCount = 0;
      let streamingMediaRecorder = null;
      let streamingMicrophone = null;
      let streamingTranscriptionParts = [];

      // WAV file creation helper
      function createWavBlob(pcmChunks, sampleRate) {
        // Calculate total length
        let totalLength = 0;
        for (let chunk of pcmChunks) {
          totalLength += chunk.length;
        }

        // Create WAV header
        const buffer = new ArrayBuffer(44 + totalLength * 2);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
          for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
          }
        };

        writeString(0, "RIFF");
        view.setUint32(4, 36 + totalLength * 2, true);
        writeString(8, "WAVE");
        writeString(12, "fmt ");
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true); // PCM
        view.setUint16(22, 1, true); // mono
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, "data");
        view.setUint32(40, totalLength * 2, true);

        // Write PCM data
        let offset = 44;
        for (let chunk of pcmChunks) {
          for (let i = 0; i < chunk.length; i++) {
            view.setInt16(offset, chunk[i], true);
            offset += 2;
          }
        }

        return new Blob([buffer], { type: "audio/wav" });
      }

      // Initialize Quill editor
      const quill = new Quill("#textEditor", {
        theme: "snow",
        placeholder:
          "Uw transcriptie en zorgverslag verschijnen hier. U kunt de tekst bewerken, formatteren en exporteren.",
        modules: {
          toolbar: [
            [{ header: [1, 2, 3, false] }],
            ["bold", "italic", "underline", "strike"],
            [{ color: [] }, { background: [] }],
            [{ list: "ordered" }, { list: "bullet" }],
            [{ indent: "-1" }, { indent: "+1" }],
            ["link", "blockquote", "code-block"],
            [{ align: [] }],
            ["clean"],
          ],
        },
      });

      // DOM elements
      const audioFileInput = document.getElementById("audioFile");
      const uploadArea = document.getElementById("uploadArea");
      const transcribeBtn = document.getElementById("transcribeBtn");
      const generateBtn = document.getElementById("generateBtn");
      const fileInfo = document.getElementById("fileInfo");
      const recordBtn = document.getElementById("recordBtn");
      const recordingInfo = document.getElementById("recordingInfo");
      const recordingTime = document.getElementById("recordingTime");
      const levelBar = document.querySelector(".level-bar");
      const loadingDiv = document.getElementById("loadingDiv");
      const loadingText = document.getElementById("loadingText");

      // Streaming elements
      const streamRecordBtn = document.getElementById("streamRecordBtn");
      const streamingInfo = document.getElementById("streamingInfo");
      const streamingTime = document.getElementById("streamingTime");
      const streamingChunks = document.getElementById("streamingChunks");
      const loadingSubtext = document.getElementById("loadingSubtext");
      const errorAlert = document.getElementById("errorAlert");
      const successAlert = document.getElementById("successAlert");
      const timingSection = document.getElementById("timingSection");
      const timingGrid = document.getElementById("timingGrid");

      // Model elements
      const modelStatus = document.getElementById("modelStatus");
      const currentModelText = document.getElementById("currentModelText");
      const runpodBtn = document.getElementById("runpodBtn");
      const whisperBtn = document.getElementById("whisperBtn");
      const wav2vec2Btn = document.getElementById("wav2vec2Btn");
      const downloadModelsBtn = document.getElementById("downloadModelsBtn");

      // Editor elements
      const tabBtns = document.querySelectorAll(".tab-btn");
      const copyBtn = document.getElementById("copyBtn");
      const printBtn = document.getElementById("printBtn");
      const downloadBtn = document.getElementById("downloadBtn");
      const clearBtn = document.getElementById("clearBtn");

      // Progress steps
      const stepUpload = document.getElementById("step-upload");
      const stepTranscribe = document.getElementById("step-transcribe");
      const stepGenerate = document.getElementById("step-generate");

      // Initialize
      window.addEventListener("load", () => {
        initializeModelStatus();
        updateProgressStep("upload", "active");
      });

      // Event listeners
      audioFileInput.addEventListener("change", handleFileSelect);
      uploadArea.addEventListener("click", () => audioFileInput.click());
      recordBtn.addEventListener("click", toggleRecording);
      transcribeBtn.addEventListener("click", handleTranscribe);
      generateBtn.addEventListener("click", handleGenerateReport);

      // Model buttons
      runpodBtn.addEventListener("click", () => switchModel("runpod"));
      whisperBtn.addEventListener("click", () => switchModel("whisper"));
      wav2vec2Btn.addEventListener("click", () => switchModel("wav2vec2"));
      downloadModelsBtn.addEventListener("click", downloadAllModels);

      // Editor buttons
      copyBtn.addEventListener("click", copyText);
      printBtn.addEventListener("click", printDocument);
      downloadBtn.addEventListener("click", downloadDocument);
      clearBtn.addEventListener("click", clearEditor);

      // Tab buttons
      tabBtns.forEach((btn) => {
        btn.addEventListener("click", () => switchTab(btn.dataset.tab));
      });

      // Drag and drop
      uploadArea.addEventListener("dragover", (e) => {
        e.preventDefault();
        uploadArea.classList.add("dragover");
      });

      uploadArea.addEventListener("dragleave", () => {
        uploadArea.classList.remove("dragover");
      });

      uploadArea.addEventListener("drop", (e) => {
        e.preventDefault();
        uploadArea.classList.remove("dragover");
        const files = e.dataTransfer.files;
        if (files.length > 0) {
          audioFileInput.files = files;
          handleFileSelect();
        }
      });

      // Functions
      function updateProgressStep(step, status) {
        const steps = ["upload", "transcribe", "generate"];
        const elements = [stepUpload, stepTranscribe, stepGenerate];

        const currentIndex = steps.indexOf(step);

        elements.forEach((el, index) => {
          el.classList.remove("active", "completed");

          if (index < currentIndex) {
            el.classList.add("completed");
          } else if (index === currentIndex) {
            el.classList.add(status);
          }
        });
      }

      function switchTab(tab) {
        activeTab = tab;

        // Update tab buttons
        tabBtns.forEach((btn) => {
          btn.classList.toggle("active", btn.dataset.tab === tab);
        });

        // Update editor content
        if (tab === "transcription" && currentTranscription) {
          quill.setText(currentTranscription);
        } else if (tab === "report" && currentReport) {
          quill.setText(currentReport);
        } else {
          quill.setText("");
        }
      }

      function handleFileSelect() {
        const file = audioFileInput.files[0];
        if (file) {
          selectedFile = file;
          const fileSize = (file.size / (1024 * 1024)).toFixed(2);

          fileInfo.innerHTML = `
                    <div class="file-info-item">
                        <span class="file-info-label">Bestand:</span>
                        <span class="file-info-value">${file.name}</span>
                    </div>
                    <div class="file-info-item">
                        <span class="file-info-label">Grootte:</span>
                        <span class="file-info-value">${fileSize} MB</span>
                    </div>
                    <div class="file-info-item">
                        <span class="file-info-label">Type:</span>
                        <span class="file-info-value">${
                          file.type || "Onbekend"
                        }</span>
                    </div>
                `;

          fileInfo.style.display = "block";
          transcribeBtn.style.display = "block";
          hideAlert();
          updateProgressStep("upload", "completed");
          updateProgressStep("transcribe", "active");
        }
      }

      async function handleTranscribe() {
        if (!selectedFile) {
          showAlert(
            "Selecteer eerst een audiobestand of maak een opname",
            "error"
          );
          return;
        }

        try {
          const modelName =
            currentModel === "wav2vec2" ? "Wav2Vec2" : "Whisper";
          showLoading(
            "Bezig met transcriptie...",
            `Dit kan even duren bij eerste gebruik (${modelName} model wordt geladen)`
          );
          hideAlert();
          timingData = {};

          currentTranscription = await transcribeAudio(selectedFile);

          // Update editor if on transcription tab
          if (activeTab === "transcription") {
            quill.setText(currentTranscription);
          }

          showTiming();
          generateBtn.style.display = "block";
          updateProgressStep("transcribe", "completed");
          updateProgressStep("generate", "active");

          showAlert("Transcriptie voltooid!", "success");
        } catch (error) {
          showAlert(error.message, "error");
          updateProgressStep("transcribe", "active");
        } finally {
          hideLoading();
        }
      }

      async function handleGenerateReport() {
        if (!currentTranscription) {
          showAlert(
            "Geen transcriptie beschikbaar. Transcribeer eerst uw audio.",
            "error"
          );
          return;
        }

        try {
          showLoading(
            "Genereren van zorgverslag...",
            "AI analyseert transcriptie en maakt rapport"
          );
          hideAlert();

          currentReport = await generateReport(currentTranscription);

          // Switch to report tab and update content
          switchTab("report");
          quill.setText(currentReport);

          showTiming();
          updateProgressStep("generate", "completed");

          showAlert("Zorgverslag succesvol gegenereerd!", "success");
        } catch (error) {
          showAlert(error.message, "error");
        } finally {
          hideLoading();
        }
      }

      async function transcribeAudio(file) {
        const formData = new FormData();
        formData.append("audio", file);
        formData.append("language", "nl"); // Force Dutch language

        const response = await fetch("/transcribe", {
          method: "POST",
          body: formData,
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
          throw new Error(result.error || "Transcriptie fout");
        }

        if (result.timing) {
          timingData.transcription = {
            ...result.timing,
            model_type: result.model_type || "whisper",
          };
        }

        return result.transcription;
      }

      async function generateReport(transcription) {
        const response = await fetch("/generate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ text: transcription }),
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
          throw new Error(result.error || "Rapport generatie fout");
        }

        if (result.timing) {
          timingData.groq = result.timing;
        }

        return result.report;
      }

      function showLoading(message, subtext = "") {
        loadingText.textContent = message;
        loadingSubtext.textContent = subtext;
        loadingDiv.classList.add("show");
        transcribeBtn.disabled = true;
        generateBtn.disabled = true;
      }

      function hideLoading() {
        loadingDiv.classList.remove("show");
        transcribeBtn.disabled = false;
        generateBtn.disabled = false;
      }

      function showAlert(message, type = "info") {
        hideAlert();
        const alert = type === "error" ? errorAlert : successAlert;
        alert.textContent = message;
        alert.classList.add("show");

        if (type === "success") {
          setTimeout(() => hideAlert(), 5000);
        }
      }

      function hideAlert() {
        errorAlert.classList.remove("show");
        successAlert.classList.remove("show");
      }

      function showTiming() {
        let html = "";
        let totalTime = 0;

        if (timingData.transcription) {
          const data = timingData.transcription;
          const modelIcon =
            data.model_type === "wav2vec2"
              ? "fas fa-volume-up"
              : "fas fa-microphone";
          const modelName =
            data.model_type === "wav2vec2" ? "Wav2Vec2" : "Whisper";

          html += `
                    <div class="timing-item">
                        <div class="timing-label"><i class="${modelIcon}"></i> ${modelName}</div>
                        <div class="timing-value">${data.total_processing_time}s</div>
                    </div>
                `;
          totalTime += data.total_processing_time;
        }

        if (timingData.groq) {
          html += `
                    <div class="timing-item">
                        <div class="timing-label"><i class="fas fa-brain"></i> AI Verwerking</div>
                        <div class="timing-value">${timingData.groq.groq_processing_time}s</div>
                    </div>
                `;
          totalTime += timingData.groq.groq_processing_time;
        }

        if (totalTime > 0) {
          html += `
                    <div class="timing-item">
                        <div class="timing-label"><i class="fas fa-stopwatch"></i> Totaal</div>
                        <div class="timing-value">${totalTime.toFixed(2)}s</div>
                    </div>
                `;
        }

        timingGrid.innerHTML = html;
        timingSection.classList.add("show");
      }

      // Recording functions
      async function toggleRecording() {
        if (mediaRecorder && mediaRecorder.state === "recording") {
          stopRecording();
        } else {
          await startRecording();
        }
      }

      async function startRecording() {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              sampleRate: 44100,
            },
          });

          // Use Web Audio API for better compatibility with RunPod
          audioContext = new (window.AudioContext || window.webkitAudioContext)(
            {
              sampleRate: 16000, // 16kHz for Whisper
            }
          );

          const source = audioContext.createMediaStreamSource(stream);

          // Create a script processor for recording
          processor = audioContext.createScriptProcessor(4096, 1, 1);

          processor.onaudioprocess = function (event) {
            if (!isRecording) return;

            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Convert Float32Array to Int16Array (WAV format)
            const int16Data = new Int16Array(inputData.length);
            for (let i = 0; i < inputData.length; i++) {
              const sample = Math.max(-1, Math.min(1, inputData[i]));
              int16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
            }

            audioChunks.push(int16Data);
          };

          source.connect(processor);
          processor.connect(audioContext.destination);

          console.log("Using Web Audio API for recording at 16kHz");

          // Fallback MediaRecorder approach if Web Audio fails
          mediaRecorder = new MediaRecorder(stream, {
            mimeType: "audio/webm;codecs=opus",
          });

          audioChunks = [];

          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              audioChunks.push(event.data);
            }
          };

          mediaRecorder.onstop = handleRecordingStop;
          setupAudioVisualization(stream);

          mediaRecorder.start(100);
          recordingStartTime = Date.now();

          recordBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Opname';
          recordBtn.classList.add("recording");
          recordingInfo.style.display = "block";

          recordingInterval = setInterval(updateRecordingTime, 1000);

          selectedFile = null;
          fileInfo.style.display = "none";
          audioFileInput.value = "";
        } catch (error) {
          showAlert("Geen toegang tot microfoon: " + error.message, "error");
        }
      }

      function stopRecording() {
        if (mediaRecorder && mediaRecorder.state === "recording") {
          mediaRecorder.stop();

          if (microphone) {
            microphone.getTracks().forEach((track) => track.stop());
          }

          recordBtn.innerHTML =
            '<i class="fas fa-microphone"></i> Start Opname';
          recordBtn.classList.remove("recording");
          recordingInfo.style.display = "none";

          if (recordingInterval) {
            clearInterval(recordingInterval);
            recordingInterval = null;
          }

          if (audioContext) {
            audioContext.close();
            audioContext = null;
          }
        }
      }

      function handleRecordingStop() {
        let blob, recordedFile;

        if (audioChunks.length > 0 && audioChunks[0] instanceof Int16Array) {
          // Web Audio API path - create WAV from PCM data
          blob = createWavBlob(audioChunks, 16000);
          recordedFile = new File([blob], `recording_${Date.now()}.wav`, {
            type: "audio/wav",
            lastModified: Date.now(),
          });
          console.log(
            "Created WAV file from Web Audio API data, size:",
            blob.size
          );
        } else {
          // Fallback - MediaRecorder path
          blob = new Blob(audioChunks, { type: "audio/webm" });
          recordedFile = new File([blob], `recording_${Date.now()}.webm`, {
            type: "audio/webm",
            lastModified: Date.now(),
          });
          console.log(
            "Using MediaRecorder webm file as fallback, size:",
            blob.size
          );
        }

        selectedFile = recordedFile;

        const duration = ((Date.now() - recordingStartTime) / 1000).toFixed(1);
        const fileSize = (blob.size / (1024 * 1024)).toFixed(2);

        fileInfo.innerHTML = `
                <div class="file-info-item">
                    <span class="file-info-label">🎤 Opname:</span>
                    <span class="file-info-value">${duration}s</span>
                </div>
                <div class="file-info-item">
                    <span class="file-info-label">Grootte:</span>
                    <span class="file-info-value">${fileSize} MB</span>
                </div>
                <div class="file-info-item">
                    <span class="file-info-label">Format:</span>
                    <span class="file-info-value">WebM/Opus</span>
                </div>
            `;

        fileInfo.style.display = "block";
        transcribeBtn.style.display = "block";
        hideAlert();
        updateProgressStep("upload", "completed");
        updateProgressStep("transcribe", "active");
      }

      function setupAudioVisualization(stream) {
        try {
          audioContext = new (window.AudioContext ||
            window.webkitAudioContext)();
          analyser = audioContext.createAnalyser();
          microphone = stream;

          const source = audioContext.createMediaStreamSource(stream);
          source.connect(analyser);

          analyser.fftSize = 256;
          const bufferLength = analyser.frequencyBinCount;
          const dataArray = new Uint8Array(bufferLength);

          function updateLevel() {
            if (mediaRecorder && mediaRecorder.state === "recording") {
              analyser.getByteFrequencyData(dataArray);

              let sum = 0;
              for (let i = 0; i < bufferLength; i++) {
                sum += dataArray[i];
              }
              const average = sum / bufferLength;
              const percentage = (average / 255) * 100;

              levelBar.style.width = percentage + "%";
              requestAnimationFrame(updateLevel);
            }
          }

          updateLevel();
        } catch (error) {
          console.warn("Audio visualization not available:", error);
        }
      }

      function updateRecordingTime() {
        if (recordingStartTime) {
          const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
          const minutes = Math.floor(elapsed / 60);
          const seconds = elapsed % 60;
          recordingTime.textContent = `${minutes
            .toString()
            .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        }
      }

      // Model management
      async function initializeModelStatus() {
        try {
          const response = await fetch("/health");
          const healthData = await response.json();

          if (response.ok) {
            updateModelUI(healthData);
          } else {
            currentModelText.textContent = "Error loading model status";
            modelStatus.classList.add("loading");
          }
        } catch (error) {
          console.error("Failed to load model status:", error);
          currentModelText.textContent = "Failed to connect to server";
          modelStatus.classList.add("loading");
        }
      }

      async function switchModel(modelType) {
        try {
          runpodBtn.disabled = true;
          whisperBtn.disabled = true;
          wav2vec2Btn.disabled = true;

          modelStatus.classList.add("loading");
          currentModelText.textContent = `Switching to ${modelType}...`;

          const response = await fetch("/switch_model", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ model_type: modelType }),
          });

          const result = await response.json();

          if (response.ok && result.success) {
            await initializeModelStatus();
            showAlert(result.message, "success");
          } else {
            throw new Error(result.error || "Failed to switch model");
          }
        } catch (error) {
          console.error("Model switch error:", error);
          showAlert("Failed to switch model: " + error.message, "error");
          await initializeModelStatus();
        } finally {
          runpodBtn.disabled = false;
          whisperBtn.disabled = false;
          wav2vec2Btn.disabled = false;
        }
      }

      async function downloadAllModels() {
        try {
          downloadModelsBtn.disabled = true;
          downloadModelsBtn.innerHTML =
            '<i class="fas fa-spinner fa-spin"></i> Downloading...';

          modelStatus.classList.add("loading");
          currentModelText.textContent =
            "Downloading models, this may take a while...";

          const response = await fetch("/download_models", {
            method: "POST",
          });

          const result = await response.json();

          if (response.ok && result.success) {
            showAlert("Models downloaded successfully!", "success");
            setTimeout(() => {
              initializeModelStatus();
            }, 3000);
          } else {
            throw new Error("Failed to download models");
          }
        } catch (error) {
          console.error("Download error:", error);
          showAlert("Failed to download models: " + error.message, "error");
          await initializeModelStatus();
        } finally {
          downloadModelsBtn.disabled = false;
          downloadModelsBtn.innerHTML =
            '<i class="fas fa-download"></i> Download Modellen';
        }
      }

      function updateModelUI(healthData) {
        currentModel = healthData.current_model_type;
        modelStatus.classList.remove("loading");

        if (currentModel === "wav2vec2") {
          currentModelText.textContent = `Wav2Vec2 (Dutch specialist) - ${
            healthData.wav2vec2_model_loaded ? "Loaded" : "Not loaded"
          }`;
        } else if (currentModel === "runpod") {
          currentModelText.textContent = `RunPod Whisper (${
            healthData.runpod_configured ? "Ready" : "Not configured"
          })`;
        } else {
          currentModelText.textContent = `Whisper (${
            healthData.whisper_model_size
          }) - ${healthData.whisper_model_loaded ? "Loaded" : "Not loaded"}`;
        }

        runpodBtn.classList.toggle("active", currentModel === "runpod");
        whisperBtn.classList.toggle("active", currentModel === "whisper");
        wav2vec2Btn.classList.toggle("active", currentModel === "wav2vec2");

        const whisperLoaded = healthData.whisper_model_loaded;
        const wav2vecLoaded = healthData.wav2vec2_model_loaded;

        if (whisperLoaded && wav2vecLoaded) {
          downloadModelsBtn.innerHTML =
            '<i class="fas fa-check"></i> Modellen Geladen';
          downloadModelsBtn.disabled = true;
        } else {
          downloadModelsBtn.innerHTML =
            '<i class="fas fa-download"></i> Download Modellen';
          downloadModelsBtn.disabled = false;
        }
      }

      // Editor functions
      function copyText() {
        const text = quill.getText();
        if (text.trim()) {
          navigator.clipboard
            .writeText(text)
            .then(() => {
              showAlert("Tekst gekopieerd naar klembord!", "success");
            })
            .catch(() => {
              showAlert("Kan tekst niet kopiëren", "error");
            });
        } else {
          showAlert("Geen tekst om te kopiëren", "error");
        }
      }

      function printDocument() {
        window.print();
      }

      function downloadDocument() {
        const text = quill.getText();
        if (text.trim()) {
          const blob = new Blob([text], { type: "text/plain" });
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `zorgverslag_${
            new Date().toISOString().split("T")[0]
          }.txt`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          showAlert("Document gedownload!", "success");
        } else {
          showAlert("Geen tekst om te downloaden", "error");
        }
      }

      function clearEditor() {
        if (confirm("Weet u zeker dat u alle tekst wilt wissen?")) {
          quill.setText("");
          currentTranscription = null;
          currentReport = null;
          showAlert("Editor gewist", "success");
        }
      }

      // =========================
      //   STREAMING FUNCTIONS
      // =========================

      function initializeSocket() {
        if (!socket) {
          socket = io();

          socket.on("connect", () => {
            console.log("Connected to streaming server");
          });

          socket.on("streaming_started", (data) => {
            streamingSession = data.session_id;
            console.log("Streaming session started:", data);
          });

          socket.on("transcription_result", (data) => {
            handleStreamingTranscription(data);
          });

          socket.on("streaming_complete", (data) => {
            handleStreamingComplete(data);
          });

          socket.on("transcription_error", (data) => {
            console.error("Streaming transcription error:", data);
            showAlert(
              `Transcriptie fout chunk ${data.chunk_id}: ${data.error}`,
              "error"
            );
          });

          socket.on("error", (data) => {
            console.error("Streaming error:", data);
            showAlert(`Streaming fout: ${data.message}`, "error");
          });
        }
      }

      function handleStreamingTranscription(data) {
        if (data.text && data.text.trim()) {
          streamingTranscriptionParts.push({
            chunk_id: data.chunk_id,
            text: data.text,
            timestamp: Date.now(),
          });

          // Update de editor met de nieuwe tekst
          const fullText = streamingTranscriptionParts
            .map((part) => part.text)
            .join(" ")
            .trim();

          quill.setText(fullText);

          // Update chunk counter
          streamingChunkCount = data.chunk_id;
          streamingChunks.textContent = `Chunks verwerkt: ${streamingChunkCount}`;

          // Scroll naar beneden in de editor
          quill.setSelection(quill.getLength());
        }
      }

      function handleStreamingComplete(data) {
        console.log("Streaming complete:", data);

        // Sla de finale transcriptie op
        currentTranscription = data.full_transcription;

        // Update de editor met de finale tekst
        if (data.full_transcription) {
          quill.setText(data.full_transcription);
        }

        showAlert(
          `Streaming transcriptie voltooid! ${data.chunk_count} chunks verwerkt.`,
          "success"
        );

        // Update file info
        updateFileInfo("streaming_recording", data.full_transcription.length);
      }

      async function startStreamingRecording() {
        if (isStreaming) {
          stopStreamingRecording();
          return;
        }

        // Check if RunPod model is selected
        if (currentModel !== "runpod") {
          showAlert(
            "Streaming werkt alleen met RunPod model. Schakel eerst over naar RunPod.",
            "error"
          );
          return;
        }

        try {
          // Initialize socket connection
          initializeSocket();

          // Get microphone access
          streamingMicrophone = await navigator.mediaDevices.getUserMedia({
            audio: {
              sampleRate: 16000,
              channelCount: 1,
              sampleSize: 16,
            },
          });

          // Use Web Audio API for streaming WAV chunks
          const streamingAudioContext = new (window.AudioContext ||
            window.webkitAudioContext)({
            sampleRate: 16000, // 16kHz for Whisper
          });

          const streamingSource =
            streamingAudioContext.createMediaStreamSource(streamingMicrophone);
          const streamingProcessor =
            streamingAudioContext.createScriptProcessor(4096, 1, 1);

          let streamingBuffer = [];
          const CHUNK_SIZE = 48000; // 3 seconds at 16kHz (increased for better speech detection)

          streamingProcessor.onaudioprocess = function (event) {
            if (!isStreaming) return;

            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Calculate audio level for this buffer
            let sum = 0;
            for (let i = 0; i < inputData.length; i++) {
              sum += Math.abs(inputData[i]);
            }
            const avgLevel = sum / inputData.length;

            // Convert Float32Array to Int16Array (WAV format)
            const int16Data = new Int16Array(inputData.length);
            for (let i = 0; i < inputData.length; i++) {
              const sample = Math.max(-1, Math.min(1, inputData[i]));
              int16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
            }

            streamingBuffer.push(...int16Data);

            // Send chunk when we have enough data (3 seconds)
            if (streamingBuffer.length >= CHUNK_SIZE) {
              const chunkData = new Int16Array(
                streamingBuffer.splice(0, CHUNK_SIZE)
              );

              // Calculate overall audio level for the chunk
              let chunkSum = 0;
              for (let i = 0; i < chunkData.length; i++) {
                chunkSum += Math.abs(chunkData[i]);
              }
              const chunkAvgLevel = chunkSum / chunkData.length;

              console.log(
                `Audio chunk: ${
                  chunkData.length
                } samples, avg level: ${chunkAvgLevel.toFixed(4)}`
              );

              // Only send chunks with sufficient audio level (threshold to filter silence)
              if (chunkAvgLevel > 50) {
                // Adjust threshold as needed
                const wavBlob = createWavBlob([chunkData], 16000);

                // Convert to base64 and send via socket
                const reader = new FileReader();
                reader.onload = () => {
                  const base64 = reader.result.split(",")[1];
                  console.log(
                    `Sending audio chunk: ${
                      base64.length
                    } chars, level: ${chunkAvgLevel.toFixed(4)}`
                  );
                  socket.emit("audio_chunk", {
                    session_id: streamingSession,
                    audio_data: base64,
                  });
                };
                reader.readAsDataURL(wavBlob);
              } else {
                console.log(
                  `Skipping silent chunk (level: ${chunkAvgLevel.toFixed(4)})`
                );
              }
            }
          };

          streamingSource.connect(streamingProcessor);
          streamingProcessor.connect(streamingAudioContext.destination);

          console.log("Using Web Audio API for streaming at 16kHz WAV chunks");

          // Fallback MediaRecorder for compatibility
          streamingMediaRecorder = new MediaRecorder(streamingMicrophone, {
            mimeType: "audio/webm;codecs=opus",
          });

          streamingMediaRecorder.ondataavailable = (event) => {
            // This is now just fallback - Web Audio API handles the main streaming
            console.log("MediaRecorder fallback triggered");
          };

          // Start recording
          streamingMediaRecorder.start(1000); // Send chunks every second

          // Update UI
          isStreaming = true;
          streamingStartTime = Date.now();
          streamingChunkCount = 0;
          streamingTranscriptionParts = [];

          streamRecordBtn.innerHTML =
            '<i class="fas fa-stop"></i> Stop Streaming';
          streamRecordBtn.classList.add("recording");
          streamingInfo.style.display = "block";

          // Clear previous content
          quill.setText("");

          // Start timing
          streamingInterval = setInterval(updateStreamingTime, 1000);

          // Start streaming session
          socket.emit("start_streaming", {
            session_id: `stream_${Date.now()}`,
            language: "nl", // Force Dutch language for streaming
          });

          showAlert(
            "Streaming opname gestart! Tekst verschijnt real-time.",
            "success"
          );
        } catch (error) {
          console.error("Streaming start error:", error);
          showAlert("Kan streaming niet starten: " + error.message, "error");
        }
      }

      function stopStreamingRecording() {
        if (!isStreaming) return;

        isStreaming = false;

        // Stop recording
        if (streamingMediaRecorder) {
          streamingMediaRecorder.stop();
        }

        if (streamingMicrophone) {
          streamingMicrophone.getTracks().forEach((track) => track.stop());
        }

        // Stop streaming session
        if (socket && streamingSession) {
          socket.emit("stop_streaming", {
            session_id: streamingSession,
          });
        }

        // Update UI
        streamRecordBtn.innerHTML =
          '<i class="fas fa-broadcast-tower"></i> Start Streaming Opname';
        streamRecordBtn.classList.remove("recording");
        streamingInfo.style.display = "none";

        if (streamingInterval) {
          clearInterval(streamingInterval);
        }

        console.log("Streaming recording stopped");
      }

      function updateStreamingTime() {
        if (streamingStartTime) {
          const elapsed = Math.floor((Date.now() - streamingStartTime) / 1000);
          const minutes = Math.floor(elapsed / 60);
          const seconds = elapsed % 60;
          streamingTime.textContent = `${minutes
            .toString()
            .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        }
      }

      // Add event listener for streaming record button
      streamRecordBtn.addEventListener("click", startStreamingRecording);
    </script>
  </body>
</html>
