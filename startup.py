#!/usr/bin/env python3
"""
Optimized startup script with loading feedback
"""
import time
import sys
import os
from app import app, socketio

def main():
    print("🚀 Starting Whisper-Groq Application...")
    print("📦 Flask initialized")
    print("⚡ Ready to accept connections on port 8932")
    print("🎤 Whisper model will load on first transcription request")
    print("🌐 Visit: http://localhost:8932")
    print("-" * 50)
    
    # Start the Flask app with environment-based debug mode
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    if debug_mode:
        print("🔧 Debug mode enabled - live reloading active!")
    app.run(host='0.0.0.0', port=8932, debug=debug_mode)

if __name__ == '__main__':
    main()